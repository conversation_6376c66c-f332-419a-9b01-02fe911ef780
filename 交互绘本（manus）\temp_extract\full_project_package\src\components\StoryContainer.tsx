// 集成OpenAI服务到主应用
// 添加到StoryContainer组件中

import { useState, useEffect } from 'react';
import storyData from '../data/storyData';
import { StoryPage } from './StoryPage';
import { Button } from './ui/button';
import { Progress } from './ui/progress';
// @ts-ignore
import { initializeOpenAI, StoryGenerator } from '../services/openAIComponents';

interface UserResponse {
  pageId: number;
  response: string;
}

export function StoryContainer() {
  const [currentPageIndex, setCurrentPageIndex] = useState(0);
  const [userResponses, setUserResponses] = useState<UserResponse[]>([]);
  const [showIntro, setShowIntro] = useState(true);
  const [showReport, setShowReport] = useState(false);
  const [analysisReport, setAnalysisReport] = useState<any>(null);
  const [apiKeySet, setApiKeySet] = useState(false);
  const [showApiKeyInput, setShowApiKeyInput] = useState(false);
  const [apiKey, setApiKey] = useState('');
  const [apiKeyError, setApiKeyError] = useState('');
  
  const totalPages = storyData.pages.length;
  const progress = ((currentPageIndex + 1) / totalPages) * 100;
  const currentPage = storyData.pages[currentPageIndex];
  
  // 初始化API密钥
  useEffect(() => {
    // 检查是否有存储的API密钥
    const storedApiKey = localStorage.getItem('openai_api_key');
    if (storedApiKey) {
      try {
        const success = initializeOpenAI(storedApiKey);
        setApiKeySet(success);
        if (success) {
          setApiKey(storedApiKey);
        }
      } catch (error) {
        console.error('初始化API密钥失败:', error);
      }
    }
  }, []);
  
  // 设置API密钥
  const handleSetApiKey = () => {
    if (!apiKey.trim()) {
      setApiKeyError('请输入有效的API密钥');
      return;
    }
    
    try {
      const success = initializeOpenAI(apiKey);
      if (success) {
        setApiKeySet(true);
        setApiKeyError('');
        setShowApiKeyInput(false);
        // 存储API密钥
        localStorage.setItem('openai_api_key', apiKey);
      } else {
        setApiKeyError('API密钥设置失败');
      }
    } catch (error) {
      console.error('设置API密钥失败:', error);
      setApiKeyError('API密钥设置失败: ' + (error as Error).message);
    }
  };
  
  const handleNext = () => {
    // 确保在翻页时停止当前页面的语音
    if ('speechSynthesis' in window) {
      window.speechSynthesis.cancel();
    }
    
    if (currentPageIndex < totalPages - 1) {
      setCurrentPageIndex(currentPageIndex + 1);
      window.scrollTo(0, 0);
    } else {
      generateReport();
    }
  };
  
  const handlePrevious = () => {
    // 确保在翻页时停止当前页面的语音
    if ('speechSynthesis' in window) {
      window.speechSynthesis.cancel();
    }
    
    if (currentPageIndex > 0) {
      setCurrentPageIndex(currentPageIndex - 1);
      window.scrollTo(0, 0);
    }
  };
  
  const handleResponseSubmit = (pageId: number, response: string) => {
    // 检查是否已有该页面的回答
    const existingResponseIndex = userResponses.findIndex(r => r.pageId === pageId);
    
    if (existingResponseIndex >= 0) {
      // 更新现有回答
      const updatedResponses = [...userResponses];
      updatedResponses[existingResponseIndex] = { pageId, response };
      setUserResponses(updatedResponses);
    } else {
      // 添加新回答
      setUserResponses([...userResponses, { pageId, response }]);
    }
  };
  
  const startStory = () => {
    setShowIntro(false);
  };
  
  const generateReport = () => {
    // 基于回答长度和关键词的简单分析
    const interactivePages = storyData.pages.filter(page => page.isInteractive);
    const completedInteractions = userResponses.length;
    
    // 初始化分数
    let languageScore = 0;
    let logicScore = 0;
    let socialScore = 0;
    let emotionalScore = 0;
    
    // 分析每个回答
    userResponses.forEach(response => {
      const text = response.response.toLowerCase();
      const words = text.split(/\s+/);
      const uniqueWords = new Set(words);
      
      // 语言词汇量：基于回答长度和多样性
      languageScore += Math.min(words.length / 5, 5) + Math.min(uniqueWords.size / 3, 5);
      
      // 逻辑思维：基于因果词汇
      const logicWords = ["因为", "所以", "如果", "但是", "然后", "接着", "首先", "其次", "最后"];
      const logicCount = logicWords.filter(word => text.includes(word)).length;
      logicScore += Math.min(logicCount * 2, 5);
      
      // 社会适应：基于社交互动词汇
      const socialWords = ["朋友", "一起", "帮助", "分享", "谢谢", "请", "对不起", "合作", "玩"];
      const socialCount = socialWords.filter(word => text.includes(word)).length;
      socialScore += Math.min(socialCount * 2, 5);
      
      // 情感识别：基于情感词汇
      const emotionWords = ["高兴", "难过", "害怕", "生气", "担心", "开心", "喜欢", "爱", "紧张", "兴奋"];
      const emotionCount = emotionWords.filter(word => text.includes(word)).length;
      emotionalScore += Math.min(emotionCount * 2, 5);
    });
    
    // 标准化分数
    const normalizeScore = (score: number) => {
      if (completedInteractions === 0) return 0;
      return Math.min(Math.round((score / completedInteractions) * 10) / 10, 5);
    };
    
    const report = {
      completedInteractions,
      totalInteractions: interactivePages.length,
      scores: {
        languageVocabulary: normalizeScore(languageScore),
        logicalThinking: normalizeScore(logicScore),
        socialAdaptation: normalizeScore(socialScore),
        emotionalRecognition: normalizeScore(emotionalScore)
      },
      recommendations: generateRecommendations(
        normalizeScore(languageScore),
        normalizeScore(logicScore),
        normalizeScore(socialScore),
        normalizeScore(emotionalScore)
      )
    };
    
    setAnalysisReport(report);
    setShowReport(true);
  };
  
  const generateRecommendations = (
    languageScore: number,
    logicScore: number,
    socialScore: number,
    emotionalScore: number
  ) => {
    const recommendations = {
      languageVocabulary: '',
      logicalThinking: '',
      socialAdaptation: '',
      emotionalRecognition: '',
      overall: ''
    };
    
    // 语言建议
    if (languageScore < 2) {
      recommendations.languageVocabulary = "词汇量较为有限，表达方式简单。建议通过更多的阅读和对话活动，扩展孩子的词汇库。";
    } else if (languageScore < 4) {
      recommendations.languageVocabulary = "具备基本的词汇表达能力，能够使用简单句型进行交流。建议鼓励使用更丰富的形容词和动词。";
    } else {
      recommendations.languageVocabulary = "词汇量丰富，能够使用多样化的词汇进行表达。建议继续通过阅读拓展专业领域词汇。";
    }
    
    // 逻辑建议
    if (logicScore < 2) {
      recommendations.logicalThinking = "逻辑表达能力需要加强，因果关系理解有限。建议通过简单的推理游戏培养逻辑思维能力。";
    } else if (logicScore < 4) {
      recommendations.logicalThinking = "能够理解基本的因果关系，表达有一定的逻辑性。建议通过更复杂的问题解决活动提升逻辑思维。";
    } else {
      recommendations.logicalThinking = "逻辑思维能力较强，能够清晰地表达因果关系和推理过程。建议尝试更复杂的逻辑推理活动。";
    }
    
    // 社交建议
    if (socialScore < 2) {
      recommendations.socialAdaptation = "社交互动意识较弱，对社交规则理解有限。建议通过角色扮演游戏培养基本社交技能。";
    } else if (socialScore < 4) {
      recommendations.socialAdaptation = "具备基本的社交意识，能够理解简单的社交规则。建议增加小组活动，提升社交互动能力。";
    } else {
      recommendations.socialAdaptation = "社交适应能力良好，能够理解并应用社交规则。建议参与更多团体活动，进一步提升社交能力。";
    }
    
    // 情感建议
    if (emotionalScore < 2) {
      recommendations.emotionalRecognition = "情感识别和表达能力有限，难以准确表达自身情感。建议通过情绪卡片游戏增强情感识别能力。";
    } else if (emotionalScore < 4) {
      recommendations.emotionalRecognition = "能够识别基本情绪，有一定的情感表达能力。建议通过讨论故事人物情感，提升情感理解深度。";
    } else {
      recommendations.emotionalRecognition = "情感识别能力较强，能够准确表达和理解多种情绪。建议探索更复杂的情感状态和共情能力培养。";
    }
    
    // 整体建议
    const avgScore = (languageScore + logicScore + socialScore + emotionalScore) / 4;
    if (avgScore < 2) {
      recommendations.overall = "建议增加日常交流互动，使用简单明确的语言，配合视觉提示辅助理解。可以通过结构化的社交故事和游戏，逐步提升语言表达和社交能力。";
    } else if (avgScore < 4) {
      recommendations.overall = "孩子具备基本的交流能力，建议通过更多的小组活动和角色扮演，提升社交互动质量。同时，可以引导孩子表达更复杂的情感和想法，培养共情能力。";
    } else {
      recommendations.overall = "孩子在语言交流方面表现良好，建议提供更具挑战性的社交情境，如解决冲突、协商合作等，进一步提升高阶社交能力和情感表达深度。";
    }
    
    return recommendations;
  };
  
  const restartStory = () => {
    setCurrentPageIndex(0);
    setUserResponses([]);
    setShowReport(false);
    setShowIntro(true);
  };
  
  // 使用OpenAI生成新故事
  const handleGenerateNewStory = async (story: any) => {
    // 这里可以实现使用OpenAI生成的故事替换当前故事
    console.log('生成的新故事:', story);
    // 重置状态并开始新故事
    setCurrentPageIndex(0);
    setUserResponses([]);
    setShowReport(false);
    setShowIntro(false);
  };
  
  if (showIntro) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-amber-50">
        <div className="max-w-2xl w-full bg-white rounded-lg shadow-xl p-8">
          <h1 className="text-3xl font-bold text-center mb-6">{storyData.title}</h1>
          <div className="mb-6">
            <p className="text-lg mb-4">这是一个为{storyData.ageGroup}自闭症儿童设计的交互式绘本，主题是"{storyData.theme}"。</p>
            <p className="mb-4">在这个故事中，你将跟随小熊波波的冒险，学习友谊的重要性。故事中有3个交互环节，你需要回答问题，帮助波波做出选择。</p>
            <p className="mb-4">完成所有交互后，系统会生成一份评估报告，分析你在语言词汇量、思维逻辑、社会适应和情感识别四个维度的表现。</p>
            <p className="mb-4 font-semibold text-blue-600">新功能：本绘本支持语音朗读和语音输入，让体验更加便捷！</p>
            
            {!apiKeySet && (
              <div className="mt-6 mb-4">
                <Button 
                  variant="outline" 
                  onClick={() => setShowApiKeyInput(!showApiKeyInput)}
                >
                  {showApiKeyInput ? '隐藏API密钥输入' : '设置OpenAI API密钥'}
                </Button>
                
                {showApiKeyInput && (
                  <div className="mt-4 p-4 border rounded-md">
                    <p className="mb-2">设置OpenAI API密钥以启用AI故事和图片生成功能</p>
                    <div className="flex gap-2">
                      <input 
                        type="password" 
                        value={apiKey}
                        onChange={(e) => setApiKey(e.target.value)}
                        placeholder="输入OpenAI API密钥"
                        className="flex-1 p-2 border rounded"
                      />
                      <Button onClick={handleSetApiKey}>设置</Button>
                    </div>
                    {apiKeyError && (
                      <p className="mt-2 text-red-500">{apiKeyError}</p>
                    )}
                  </div>
                )}
              </div>
            )}
            
            {apiKeySet && (
              <div className="mt-6 mb-4">
                <p className="text-green-600 mb-2">✓ OpenAI API密钥已设置，AI生成功能已启用</p>
                <StoryGenerator 
                  onStoryGenerated={handleGenerateNewStory}
                  ageRange={storyData.ageGroup}
                  theme={storyData.theme}
                />
              </div>
            )}
          </div>
          <div className="flex justify-center">
            <Button onClick={startStory} size="lg">开始阅读</Button>
          </div>
        </div>
      </div>
    );
  }
  
  if (showReport) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-amber-50">
        <div className="max-w-3xl w-full bg-white rounded-lg shadow-xl p-8">
          <h1 className="text-3xl font-bold text-center mb-6">自闭症儿童语言交互能力评估报告</h1>
          
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-2">基本信息</h2>
            <p>年龄段：{storyData.ageGroup}</p>
            <p>绘本主题：{storyData.theme}</p>
            <p>完成交互环节数量：{analysisReport.completedInteractions}/{analysisReport.totalInteractions}</p>
          </div>
          
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-4">能力维度评估（满分5分）</h2>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between mb-1">
                  <span>语言词汇量：{analysisReport.scores.languageVocabulary}分</span>
                  <span>{analysisReport.scores.languageVocabulary}/5</span>
                </div>
                <Progress value={analysisReport.scores.languageVocabulary * 20} className="h-2" />
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span>思维逻辑：{analysisReport.scores.logicalThinking}分</span>
                  <span>{analysisReport.scores.logicalThinking}/5</span>
                </div>
                <Progress value={analysisReport.scores.logicalThinking * 20} className="h-2" />
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span>社会适应：{analysisReport.scores.socialAdaptation}分</span>
                  <span>{analysisReport.scores.socialAdaptation}/5</span>
                </div>
                <Progress value={analysisReport.scores.socialAdaptation * 20} className="h-2" />
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span>情感识别：{analysisReport.scores.emotionalRecognition}分</span>
                  <span>{analysisReport.scores.emotionalRecognition}/5</span>
                </div>
                <Progress value={analysisReport.scores.emotionalRecognition * 20} className="h-2" />
              </div>
            </div>
          </div>
          
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-2">详细分析</h2>
            <div className="space-y-4">
              <div>
                <h3 className="font-medium">语言词汇量</h3>
                <p>{analysisReport.recommendations.languageVocabulary}</p>
              </div>
              <div>
                <h3 className="font-medium">思维逻辑</h3>
                <p>{analysisReport.recommendations.logicalThinking}</p>
              </div>
              <div>
                <h3 className="font-medium">社会适应</h3>
                <p>{analysisReport.recommendations.socialAdaptation}</p>
              </div>
              <div>
                <h3 className="font-medium">情感识别</h3>
                <p>{analysisReport.recommendations.emotionalRecognition}</p>
              </div>
            </div>
          </div>
          
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-2">总结建议</h2>
            <p>{analysisReport.recommendations.overall}</p>
          </div>
          
          <div className="flex justify-center">
            <Button onClick={restartStory} size="lg">重新开始</Button>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-amber-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <header className="mb-6">
          <h1 className="text-3xl font-bold text-center">{storyData.title}</h1>
          <div className="mt-4">
            <div className="flex justify-between text-sm mb-1">
              <span>第 {currentPageIndex + 1} 页，共 {totalPages} 页</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        </header>
        
        <StoryPage 
          page={currentPage} 
          onNext={handleNext} 
          onResponseSubmit={handleResponseSubmit}
          userResponses={userResponses}
        />
        
        <div className="flex justify-between mt-6">
          <Button 
            variant="outline" 
            onClick={handlePrevious}
            disabled={currentPageIndex === 0}
          >
            上一页
          </Button>
          
          {!currentPage.isInteractive && (
            <Button 
              onClick={handleNext}
              disabled={currentPageIndex === totalPages - 1}
            >
              {currentPageIndex === totalPages - 1 ? '完成阅读' : '下一页'}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
