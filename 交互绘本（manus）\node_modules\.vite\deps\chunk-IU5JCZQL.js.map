{"version": 3, "sources": ["../../react/cjs/react.development.js", "../../react/index.js"], "sourcesContent": ["/**\n * @license React\n * react.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n\n          'use strict';\n\n/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n}\n          var ReactVersion = '18.3.1';\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\n/**\n * Keeps track of the current dispatcher.\n */\nvar ReactCurrentDispatcher = {\n  /**\n   * @internal\n   * @type {ReactComponent}\n   */\n  current: null\n};\n\n/**\n * Keeps track of the current batch's configuration such as how long an update\n * should suspend for if it needs to.\n */\nvar ReactCurrentBatchConfig = {\n  transition: null\n};\n\nvar ReactCurrentActQueue = {\n  current: null,\n  // Used to reproduce behavior of `batchedUpdates` in legacy mode.\n  isBatchingLegacy: false,\n  didScheduleLegacyUpdate: false\n};\n\n/**\n * Keeps track of the current owner.\n *\n * The current owner is the component who should own any components that are\n * currently being constructed.\n */\nvar ReactCurrentOwner = {\n  /**\n   * @internal\n   * @type {ReactComponent}\n   */\n  current: null\n};\n\nvar ReactDebugCurrentFrame = {};\nvar currentExtraStackFrame = null;\nfunction setExtraStackFrame(stack) {\n  {\n    currentExtraStackFrame = stack;\n  }\n}\n\n{\n  ReactDebugCurrentFrame.setExtraStackFrame = function (stack) {\n    {\n      currentExtraStackFrame = stack;\n    }\n  }; // Stack implementation injected by the current renderer.\n\n\n  ReactDebugCurrentFrame.getCurrentStack = null;\n\n  ReactDebugCurrentFrame.getStackAddendum = function () {\n    var stack = ''; // Add an extra top frame while an element is being validated\n\n    if (currentExtraStackFrame) {\n      stack += currentExtraStackFrame;\n    } // Delegate to the injected renderer-specific implementation\n\n\n    var impl = ReactDebugCurrentFrame.getCurrentStack;\n\n    if (impl) {\n      stack += impl() || '';\n    }\n\n    return stack;\n  };\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false; // Track which Fiber(s) schedule render work.\n\nvar ReactSharedInternals = {\n  ReactCurrentDispatcher: ReactCurrentDispatcher,\n  ReactCurrentBatchConfig: ReactCurrentBatchConfig,\n  ReactCurrentOwner: ReactCurrentOwner\n};\n\n{\n  ReactSharedInternals.ReactDebugCurrentFrame = ReactDebugCurrentFrame;\n  ReactSharedInternals.ReactCurrentActQueue = ReactCurrentActQueue;\n}\n\n// by calls to these methods by a Babel plugin.\n//\n// In PROD (or in packages without access to React internals),\n// they are left as they are instead.\n\nfunction warn(format) {\n  {\n    {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n\n      printWarning('warn', format, args);\n    }\n  }\n}\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\nvar didWarnStateUpdateForUnmountedComponent = {};\n\nfunction warnNoop(publicInstance, callerName) {\n  {\n    var _constructor = publicInstance.constructor;\n    var componentName = _constructor && (_constructor.displayName || _constructor.name) || 'ReactClass';\n    var warningKey = componentName + \".\" + callerName;\n\n    if (didWarnStateUpdateForUnmountedComponent[warningKey]) {\n      return;\n    }\n\n    error(\"Can't call %s on a component that is not yet mounted. \" + 'This is a no-op, but it might indicate a bug in your application. ' + 'Instead, assign to `this.state` directly or define a `state = {};` ' + 'class property with the desired state in the %s component.', callerName, componentName);\n\n    didWarnStateUpdateForUnmountedComponent[warningKey] = true;\n  }\n}\n/**\n * This is the abstract API for an update queue.\n */\n\n\nvar ReactNoopUpdateQueue = {\n  /**\n   * Checks whether or not this composite component is mounted.\n   * @param {ReactClass} publicInstance The instance we want to test.\n   * @return {boolean} True if mounted, false otherwise.\n   * @protected\n   * @final\n   */\n  isMounted: function (publicInstance) {\n    return false;\n  },\n\n  /**\n   * Forces an update. This should only be invoked when it is known with\n   * certainty that we are **not** in a DOM transaction.\n   *\n   * You may want to call this when you know that some deeper aspect of the\n   * component's state has changed but `setState` was not called.\n   *\n   * This will not invoke `shouldComponentUpdate`, but it will invoke\n   * `componentWillUpdate` and `componentDidUpdate`.\n   *\n   * @param {ReactClass} publicInstance The instance that should rerender.\n   * @param {?function} callback Called after component is updated.\n   * @param {?string} callerName name of the calling function in the public API.\n   * @internal\n   */\n  enqueueForceUpdate: function (publicInstance, callback, callerName) {\n    warnNoop(publicInstance, 'forceUpdate');\n  },\n\n  /**\n   * Replaces all of the state. Always use this or `setState` to mutate state.\n   * You should treat `this.state` as immutable.\n   *\n   * There is no guarantee that `this.state` will be immediately updated, so\n   * accessing `this.state` after calling this method may return the old value.\n   *\n   * @param {ReactClass} publicInstance The instance that should rerender.\n   * @param {object} completeState Next state.\n   * @param {?function} callback Called after component is updated.\n   * @param {?string} callerName name of the calling function in the public API.\n   * @internal\n   */\n  enqueueReplaceState: function (publicInstance, completeState, callback, callerName) {\n    warnNoop(publicInstance, 'replaceState');\n  },\n\n  /**\n   * Sets a subset of the state. This only exists because _pendingState is\n   * internal. This provides a merging strategy that is not available to deep\n   * properties which is confusing. TODO: Expose pendingState or don't use it\n   * during the merge.\n   *\n   * @param {ReactClass} publicInstance The instance that should rerender.\n   * @param {object} partialState Next partial state to be merged with state.\n   * @param {?function} callback Called after component is updated.\n   * @param {?string} Name of the calling function in the public API.\n   * @internal\n   */\n  enqueueSetState: function (publicInstance, partialState, callback, callerName) {\n    warnNoop(publicInstance, 'setState');\n  }\n};\n\nvar assign = Object.assign;\n\nvar emptyObject = {};\n\n{\n  Object.freeze(emptyObject);\n}\n/**\n * Base class helpers for the updating state of a component.\n */\n\n\nfunction Component(props, context, updater) {\n  this.props = props;\n  this.context = context; // If a component has string refs, we will assign a different object later.\n\n  this.refs = emptyObject; // We initialize the default updater but the real one gets injected by the\n  // renderer.\n\n  this.updater = updater || ReactNoopUpdateQueue;\n}\n\nComponent.prototype.isReactComponent = {};\n/**\n * Sets a subset of the state. Always use this to mutate\n * state. You should treat `this.state` as immutable.\n *\n * There is no guarantee that `this.state` will be immediately updated, so\n * accessing `this.state` after calling this method may return the old value.\n *\n * There is no guarantee that calls to `setState` will run synchronously,\n * as they may eventually be batched together.  You can provide an optional\n * callback that will be executed when the call to setState is actually\n * completed.\n *\n * When a function is provided to setState, it will be called at some point in\n * the future (not synchronously). It will be called with the up to date\n * component arguments (state, props, context). These values can be different\n * from this.* because your function may be called after receiveProps but before\n * shouldComponentUpdate, and this new state, props, and context will not yet be\n * assigned to this.\n *\n * @param {object|function} partialState Next partial state or function to\n *        produce next partial state to be merged with current state.\n * @param {?function} callback Called after state is updated.\n * @final\n * @protected\n */\n\nComponent.prototype.setState = function (partialState, callback) {\n  if (typeof partialState !== 'object' && typeof partialState !== 'function' && partialState != null) {\n    throw new Error('setState(...): takes an object of state variables to update or a ' + 'function which returns an object of state variables.');\n  }\n\n  this.updater.enqueueSetState(this, partialState, callback, 'setState');\n};\n/**\n * Forces an update. This should only be invoked when it is known with\n * certainty that we are **not** in a DOM transaction.\n *\n * You may want to call this when you know that some deeper aspect of the\n * component's state has changed but `setState` was not called.\n *\n * This will not invoke `shouldComponentUpdate`, but it will invoke\n * `componentWillUpdate` and `componentDidUpdate`.\n *\n * @param {?function} callback Called after update is complete.\n * @final\n * @protected\n */\n\n\nComponent.prototype.forceUpdate = function (callback) {\n  this.updater.enqueueForceUpdate(this, callback, 'forceUpdate');\n};\n/**\n * Deprecated APIs. These APIs used to exist on classic React classes but since\n * we would like to deprecate them, we're not going to move them over to this\n * modern base class. Instead, we define a getter that warns if it's accessed.\n */\n\n\n{\n  var deprecatedAPIs = {\n    isMounted: ['isMounted', 'Instead, make sure to clean up subscriptions and pending requests in ' + 'componentWillUnmount to prevent memory leaks.'],\n    replaceState: ['replaceState', 'Refactor your code to use setState instead (see ' + 'https://github.com/facebook/react/issues/3236).']\n  };\n\n  var defineDeprecationWarning = function (methodName, info) {\n    Object.defineProperty(Component.prototype, methodName, {\n      get: function () {\n        warn('%s(...) is deprecated in plain JavaScript React classes. %s', info[0], info[1]);\n\n        return undefined;\n      }\n    });\n  };\n\n  for (var fnName in deprecatedAPIs) {\n    if (deprecatedAPIs.hasOwnProperty(fnName)) {\n      defineDeprecationWarning(fnName, deprecatedAPIs[fnName]);\n    }\n  }\n}\n\nfunction ComponentDummy() {}\n\nComponentDummy.prototype = Component.prototype;\n/**\n * Convenience component with default shallow equality check for sCU.\n */\n\nfunction PureComponent(props, context, updater) {\n  this.props = props;\n  this.context = context; // If a component has string refs, we will assign a different object later.\n\n  this.refs = emptyObject;\n  this.updater = updater || ReactNoopUpdateQueue;\n}\n\nvar pureComponentPrototype = PureComponent.prototype = new ComponentDummy();\npureComponentPrototype.constructor = PureComponent; // Avoid an extra prototype jump for these methods.\n\nassign(pureComponentPrototype, Component.prototype);\npureComponentPrototype.isPureReactComponent = true;\n\n// an immutable object with a single mutable value\nfunction createRef() {\n  var refObject = {\n    current: null\n  };\n\n  {\n    Object.seal(refObject);\n  }\n\n  return refObject;\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n/*\n * The `'' + value` pattern (used in in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object';\n    return type;\n  }\n} // $FlowFixMe only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n} // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  {\n    if (typeof type.tag === 'number') {\n      error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n    }\n  }\n\n  if (typeof type === 'function') {\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n        return getContextName(context) + '.Consumer';\n\n      case REACT_PROVIDER_TYPE:\n        var provider = type;\n        return getContextName(provider._context) + '.Provider';\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n\n      // eslint-disable-next-line no-fallthrough\n    }\n  }\n\n  return null;\n}\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar RESERVED_PROPS = {\n  key: true,\n  ref: true,\n  __self: true,\n  __source: true\n};\nvar specialPropKeyWarningShown, specialPropRefWarningShown, didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  var warnAboutAccessingKey = function () {\n    {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    }\n  };\n\n  warnAboutAccessingKey.isReactWarning = true;\n  Object.defineProperty(props, 'key', {\n    get: warnAboutAccessingKey,\n    configurable: true\n  });\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  var warnAboutAccessingRef = function () {\n    {\n      if (!specialPropRefWarningShown) {\n        specialPropRefWarningShown = true;\n\n        error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    }\n  };\n\n  warnAboutAccessingRef.isReactWarning = true;\n  Object.defineProperty(props, 'ref', {\n    get: warnAboutAccessingRef,\n    configurable: true\n  });\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && config.__self && ReactCurrentOwner.current.stateNode !== config.__self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', componentName, config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nvar ReactElement = function (type, key, ref, self, source, owner, props) {\n  var element = {\n    // This tag allows us to uniquely identify this as a React Element\n    $$typeof: REACT_ELEMENT_TYPE,\n    // Built-in properties that belong on the element\n    type: type,\n    key: key,\n    ref: ref,\n    props: props,\n    // Record the component responsible for creating this element.\n    _owner: owner\n  };\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // self and source are DEV only properties.\n\n    Object.defineProperty(element, '_self', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: self\n    }); // Two elements created in two different places should be considered\n    // equal for testing purposes and therefore we hide it from enumeration.\n\n    Object.defineProperty(element, '_source', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: source\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n};\n/**\n * Create and return a new ReactElement of the given type.\n * See https://reactjs.org/docs/react-api.html#createelement\n */\n\nfunction createElement(type, config, children) {\n  var propName; // Reserved names are extracted\n\n  var props = {};\n  var key = null;\n  var ref = null;\n  var self = null;\n  var source = null;\n\n  if (config != null) {\n    if (hasValidRef(config)) {\n      ref = config.ref;\n\n      {\n        warnIfStringRefCannotBeAutoConverted(config);\n      }\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    self = config.__self === undefined ? null : config.__self;\n    source = config.__source === undefined ? null : config.__source; // Remaining properties are added to a new props object\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        props[propName] = config[propName];\n      }\n    }\n  } // Children can be more than one argument, and those are transferred onto\n  // the newly allocated props object.\n\n\n  var childrenLength = arguments.length - 2;\n\n  if (childrenLength === 1) {\n    props.children = children;\n  } else if (childrenLength > 1) {\n    var childArray = Array(childrenLength);\n\n    for (var i = 0; i < childrenLength; i++) {\n      childArray[i] = arguments[i + 2];\n    }\n\n    {\n      if (Object.freeze) {\n        Object.freeze(childArray);\n      }\n    }\n\n    props.children = childArray;\n  } // Resolve default props\n\n\n  if (type && type.defaultProps) {\n    var defaultProps = type.defaultProps;\n\n    for (propName in defaultProps) {\n      if (props[propName] === undefined) {\n        props[propName] = defaultProps[propName];\n      }\n    }\n  }\n\n  {\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n  }\n\n  return ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n}\nfunction cloneAndReplaceKey(oldElement, newKey) {\n  var newElement = ReactElement(oldElement.type, newKey, oldElement.ref, oldElement._self, oldElement._source, oldElement._owner, oldElement.props);\n  return newElement;\n}\n/**\n * Clone and return a new ReactElement using element as the starting point.\n * See https://reactjs.org/docs/react-api.html#cloneelement\n */\n\nfunction cloneElement(element, config, children) {\n  if (element === null || element === undefined) {\n    throw new Error(\"React.cloneElement(...): The argument must be a React element, but you passed \" + element + \".\");\n  }\n\n  var propName; // Original props are copied\n\n  var props = assign({}, element.props); // Reserved names are extracted\n\n  var key = element.key;\n  var ref = element.ref; // Self is preserved since the owner is preserved.\n\n  var self = element._self; // Source is preserved since cloneElement is unlikely to be targeted by a\n  // transpiler, and the original source is probably a better indicator of the\n  // true owner.\n\n  var source = element._source; // Owner will be preserved, unless ref is overridden\n\n  var owner = element._owner;\n\n  if (config != null) {\n    if (hasValidRef(config)) {\n      // Silently steal the ref from the parent.\n      ref = config.ref;\n      owner = ReactCurrentOwner.current;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    } // Remaining properties override existing props\n\n\n    var defaultProps;\n\n    if (element.type && element.type.defaultProps) {\n      defaultProps = element.type.defaultProps;\n    }\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        if (config[propName] === undefined && defaultProps !== undefined) {\n          // Resolve default props\n          props[propName] = defaultProps[propName];\n        } else {\n          props[propName] = config[propName];\n        }\n      }\n    }\n  } // Children can be more than one argument, and those are transferred onto\n  // the newly allocated props object.\n\n\n  var childrenLength = arguments.length - 2;\n\n  if (childrenLength === 1) {\n    props.children = children;\n  } else if (childrenLength > 1) {\n    var childArray = Array(childrenLength);\n\n    for (var i = 0; i < childrenLength; i++) {\n      childArray[i] = arguments[i + 2];\n    }\n\n    props.children = childArray;\n  }\n\n  return ReactElement(element.type, key, ref, self, source, owner, props);\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\nfunction isValidElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\n\nvar SEPARATOR = '.';\nvar SUBSEPARATOR = ':';\n/**\n * Escape and wrap key so it is safe to use as a reactid\n *\n * @param {string} key to be escaped.\n * @return {string} the escaped key.\n */\n\nfunction escape(key) {\n  var escapeRegex = /[=:]/g;\n  var escaperLookup = {\n    '=': '=0',\n    ':': '=2'\n  };\n  var escapedString = key.replace(escapeRegex, function (match) {\n    return escaperLookup[match];\n  });\n  return '$' + escapedString;\n}\n/**\n * TODO: Test that a single child and an array with one item have the same key\n * pattern.\n */\n\n\nvar didWarnAboutMaps = false;\nvar userProvidedKeyEscapeRegex = /\\/+/g;\n\nfunction escapeUserProvidedKey(text) {\n  return text.replace(userProvidedKeyEscapeRegex, '$&/');\n}\n/**\n * Generate a key string that identifies a element within a set.\n *\n * @param {*} element A element that could contain a manual key.\n * @param {number} index Index that is used if a manual key is not provided.\n * @return {string}\n */\n\n\nfunction getElementKey(element, index) {\n  // Do some typechecking here since we call this blindly. We want to ensure\n  // that we don't block potential future ES APIs.\n  if (typeof element === 'object' && element !== null && element.key != null) {\n    // Explicit key\n    {\n      checkKeyStringCoercion(element.key);\n    }\n\n    return escape('' + element.key);\n  } // Implicit key determined by the index in the set\n\n\n  return index.toString(36);\n}\n\nfunction mapIntoArray(children, array, escapedPrefix, nameSoFar, callback) {\n  var type = typeof children;\n\n  if (type === 'undefined' || type === 'boolean') {\n    // All of the above are perceived as null.\n    children = null;\n  }\n\n  var invokeCallback = false;\n\n  if (children === null) {\n    invokeCallback = true;\n  } else {\n    switch (type) {\n      case 'string':\n      case 'number':\n        invokeCallback = true;\n        break;\n\n      case 'object':\n        switch (children.$$typeof) {\n          case REACT_ELEMENT_TYPE:\n          case REACT_PORTAL_TYPE:\n            invokeCallback = true;\n        }\n\n    }\n  }\n\n  if (invokeCallback) {\n    var _child = children;\n    var mappedChild = callback(_child); // If it's the only child, treat the name as if it was wrapped in an array\n    // so that it's consistent if the number of children grows:\n\n    var childKey = nameSoFar === '' ? SEPARATOR + getElementKey(_child, 0) : nameSoFar;\n\n    if (isArray(mappedChild)) {\n      var escapedChildKey = '';\n\n      if (childKey != null) {\n        escapedChildKey = escapeUserProvidedKey(childKey) + '/';\n      }\n\n      mapIntoArray(mappedChild, array, escapedChildKey, '', function (c) {\n        return c;\n      });\n    } else if (mappedChild != null) {\n      if (isValidElement(mappedChild)) {\n        {\n          // The `if` statement here prevents auto-disabling of the safe\n          // coercion ESLint rule, so we must manually disable it below.\n          // $FlowFixMe Flow incorrectly thinks React.Portal doesn't have a key\n          if (mappedChild.key && (!_child || _child.key !== mappedChild.key)) {\n            checkKeyStringCoercion(mappedChild.key);\n          }\n        }\n\n        mappedChild = cloneAndReplaceKey(mappedChild, // Keep both the (mapped) and old keys if they differ, just as\n        // traverseAllChildren used to do for objects as children\n        escapedPrefix + ( // $FlowFixMe Flow incorrectly thinks React.Portal doesn't have a key\n        mappedChild.key && (!_child || _child.key !== mappedChild.key) ? // $FlowFixMe Flow incorrectly thinks existing element's key can be a number\n        // eslint-disable-next-line react-internal/safe-string-coercion\n        escapeUserProvidedKey('' + mappedChild.key) + '/' : '') + childKey);\n      }\n\n      array.push(mappedChild);\n    }\n\n    return 1;\n  }\n\n  var child;\n  var nextName;\n  var subtreeCount = 0; // Count of children found in the current subtree.\n\n  var nextNamePrefix = nameSoFar === '' ? SEPARATOR : nameSoFar + SUBSEPARATOR;\n\n  if (isArray(children)) {\n    for (var i = 0; i < children.length; i++) {\n      child = children[i];\n      nextName = nextNamePrefix + getElementKey(child, i);\n      subtreeCount += mapIntoArray(child, array, escapedPrefix, nextName, callback);\n    }\n  } else {\n    var iteratorFn = getIteratorFn(children);\n\n    if (typeof iteratorFn === 'function') {\n      var iterableChildren = children;\n\n      {\n        // Warn about using Maps as children\n        if (iteratorFn === iterableChildren.entries) {\n          if (!didWarnAboutMaps) {\n            warn('Using Maps as children is not supported. ' + 'Use an array of keyed ReactElements instead.');\n          }\n\n          didWarnAboutMaps = true;\n        }\n      }\n\n      var iterator = iteratorFn.call(iterableChildren);\n      var step;\n      var ii = 0;\n\n      while (!(step = iterator.next()).done) {\n        child = step.value;\n        nextName = nextNamePrefix + getElementKey(child, ii++);\n        subtreeCount += mapIntoArray(child, array, escapedPrefix, nextName, callback);\n      }\n    } else if (type === 'object') {\n      // eslint-disable-next-line react-internal/safe-string-coercion\n      var childrenString = String(children);\n      throw new Error(\"Objects are not valid as a React child (found: \" + (childrenString === '[object Object]' ? 'object with keys {' + Object.keys(children).join(', ') + '}' : childrenString) + \"). \" + 'If you meant to render a collection of children, use an array ' + 'instead.');\n    }\n  }\n\n  return subtreeCount;\n}\n\n/**\n * Maps children that are typically specified as `props.children`.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrenmap\n *\n * The provided mapFunction(child, index) will be called for each\n * leaf child.\n *\n * @param {?*} children Children tree container.\n * @param {function(*, int)} func The map function.\n * @param {*} context Context for mapFunction.\n * @return {object} Object containing the ordered map of results.\n */\nfunction mapChildren(children, func, context) {\n  if (children == null) {\n    return children;\n  }\n\n  var result = [];\n  var count = 0;\n  mapIntoArray(children, result, '', '', function (child) {\n    return func.call(context, child, count++);\n  });\n  return result;\n}\n/**\n * Count the number of children that are typically specified as\n * `props.children`.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrencount\n *\n * @param {?*} children Children tree container.\n * @return {number} The number of children.\n */\n\n\nfunction countChildren(children) {\n  var n = 0;\n  mapChildren(children, function () {\n    n++; // Don't return anything\n  });\n  return n;\n}\n\n/**\n * Iterates through children that are typically specified as `props.children`.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrenforeach\n *\n * The provided forEachFunc(child, index) will be called for each\n * leaf child.\n *\n * @param {?*} children Children tree container.\n * @param {function(*, int)} forEachFunc\n * @param {*} forEachContext Context for forEachContext.\n */\nfunction forEachChildren(children, forEachFunc, forEachContext) {\n  mapChildren(children, function () {\n    forEachFunc.apply(this, arguments); // Don't return anything.\n  }, forEachContext);\n}\n/**\n * Flatten a children object (typically specified as `props.children`) and\n * return an array with appropriately re-keyed children.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrentoarray\n */\n\n\nfunction toArray(children) {\n  return mapChildren(children, function (child) {\n    return child;\n  }) || [];\n}\n/**\n * Returns the first child in a collection of children and verifies that there\n * is only one child in the collection.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrenonly\n *\n * The current implementation of this function assumes that a single child gets\n * passed without a wrapper, but the purpose of this helper function is to\n * abstract away the particular structure of children.\n *\n * @param {?object} children Child collection structure.\n * @return {ReactElement} The first and only `ReactElement` contained in the\n * structure.\n */\n\n\nfunction onlyChild(children) {\n  if (!isValidElement(children)) {\n    throw new Error('React.Children.only expected to receive a single React element child.');\n  }\n\n  return children;\n}\n\nfunction createContext(defaultValue) {\n  // TODO: Second argument used to be an optional `calculateChangedBits`\n  // function. Warn to reserve for future use?\n  var context = {\n    $$typeof: REACT_CONTEXT_TYPE,\n    // As a workaround to support multiple concurrent renderers, we categorize\n    // some renderers as primary and others as secondary. We only expect\n    // there to be two concurrent renderers at most: React Native (primary) and\n    // Fabric (secondary); React DOM (primary) and React ART (secondary).\n    // Secondary renderers store their context values on separate fields.\n    _currentValue: defaultValue,\n    _currentValue2: defaultValue,\n    // Used to track how many concurrent renderers this context currently\n    // supports within in a single renderer. Such as parallel server rendering.\n    _threadCount: 0,\n    // These are circular\n    Provider: null,\n    Consumer: null,\n    // Add these to use same hidden class in VM as ServerContext\n    _defaultValue: null,\n    _globalName: null\n  };\n  context.Provider = {\n    $$typeof: REACT_PROVIDER_TYPE,\n    _context: context\n  };\n  var hasWarnedAboutUsingNestedContextConsumers = false;\n  var hasWarnedAboutUsingConsumerProvider = false;\n  var hasWarnedAboutDisplayNameOnConsumer = false;\n\n  {\n    // A separate object, but proxies back to the original context object for\n    // backwards compatibility. It has a different $$typeof, so we can properly\n    // warn for the incorrect usage of Context as a Consumer.\n    var Consumer = {\n      $$typeof: REACT_CONTEXT_TYPE,\n      _context: context\n    }; // $FlowFixMe: Flow complains about not setting a value, which is intentional here\n\n    Object.defineProperties(Consumer, {\n      Provider: {\n        get: function () {\n          if (!hasWarnedAboutUsingConsumerProvider) {\n            hasWarnedAboutUsingConsumerProvider = true;\n\n            error('Rendering <Context.Consumer.Provider> is not supported and will be removed in ' + 'a future major release. Did you mean to render <Context.Provider> instead?');\n          }\n\n          return context.Provider;\n        },\n        set: function (_Provider) {\n          context.Provider = _Provider;\n        }\n      },\n      _currentValue: {\n        get: function () {\n          return context._currentValue;\n        },\n        set: function (_currentValue) {\n          context._currentValue = _currentValue;\n        }\n      },\n      _currentValue2: {\n        get: function () {\n          return context._currentValue2;\n        },\n        set: function (_currentValue2) {\n          context._currentValue2 = _currentValue2;\n        }\n      },\n      _threadCount: {\n        get: function () {\n          return context._threadCount;\n        },\n        set: function (_threadCount) {\n          context._threadCount = _threadCount;\n        }\n      },\n      Consumer: {\n        get: function () {\n          if (!hasWarnedAboutUsingNestedContextConsumers) {\n            hasWarnedAboutUsingNestedContextConsumers = true;\n\n            error('Rendering <Context.Consumer.Consumer> is not supported and will be removed in ' + 'a future major release. Did you mean to render <Context.Consumer> instead?');\n          }\n\n          return context.Consumer;\n        }\n      },\n      displayName: {\n        get: function () {\n          return context.displayName;\n        },\n        set: function (displayName) {\n          if (!hasWarnedAboutDisplayNameOnConsumer) {\n            warn('Setting `displayName` on Context.Consumer has no effect. ' + \"You should set it directly on the context with Context.displayName = '%s'.\", displayName);\n\n            hasWarnedAboutDisplayNameOnConsumer = true;\n          }\n        }\n      }\n    }); // $FlowFixMe: Flow complains about missing properties because it doesn't understand defineProperty\n\n    context.Consumer = Consumer;\n  }\n\n  {\n    context._currentRenderer = null;\n    context._currentRenderer2 = null;\n  }\n\n  return context;\n}\n\nvar Uninitialized = -1;\nvar Pending = 0;\nvar Resolved = 1;\nvar Rejected = 2;\n\nfunction lazyInitializer(payload) {\n  if (payload._status === Uninitialized) {\n    var ctor = payload._result;\n    var thenable = ctor(); // Transition to the next state.\n    // This might throw either because it's missing or throws. If so, we treat it\n    // as still uninitialized and try again next time. Which is the same as what\n    // happens if the ctor or any wrappers processing the ctor throws. This might\n    // end up fixing it if the resolution was a concurrency bug.\n\n    thenable.then(function (moduleObject) {\n      if (payload._status === Pending || payload._status === Uninitialized) {\n        // Transition to the next state.\n        var resolved = payload;\n        resolved._status = Resolved;\n        resolved._result = moduleObject;\n      }\n    }, function (error) {\n      if (payload._status === Pending || payload._status === Uninitialized) {\n        // Transition to the next state.\n        var rejected = payload;\n        rejected._status = Rejected;\n        rejected._result = error;\n      }\n    });\n\n    if (payload._status === Uninitialized) {\n      // In case, we're still uninitialized, then we're waiting for the thenable\n      // to resolve. Set it as pending in the meantime.\n      var pending = payload;\n      pending._status = Pending;\n      pending._result = thenable;\n    }\n  }\n\n  if (payload._status === Resolved) {\n    var moduleObject = payload._result;\n\n    {\n      if (moduleObject === undefined) {\n        error('lazy: Expected the result of a dynamic imp' + 'ort() call. ' + 'Instead received: %s\\n\\nYour code should look like: \\n  ' + // Break up imports to avoid accidentally parsing them as dependencies.\n        'const MyComponent = lazy(() => imp' + \"ort('./MyComponent'))\\n\\n\" + 'Did you accidentally put curly braces around the import?', moduleObject);\n      }\n    }\n\n    {\n      if (!('default' in moduleObject)) {\n        error('lazy: Expected the result of a dynamic imp' + 'ort() call. ' + 'Instead received: %s\\n\\nYour code should look like: \\n  ' + // Break up imports to avoid accidentally parsing them as dependencies.\n        'const MyComponent = lazy(() => imp' + \"ort('./MyComponent'))\", moduleObject);\n      }\n    }\n\n    return moduleObject.default;\n  } else {\n    throw payload._result;\n  }\n}\n\nfunction lazy(ctor) {\n  var payload = {\n    // We use these fields to store the result.\n    _status: Uninitialized,\n    _result: ctor\n  };\n  var lazyType = {\n    $$typeof: REACT_LAZY_TYPE,\n    _payload: payload,\n    _init: lazyInitializer\n  };\n\n  {\n    // In production, this would just set it on the object.\n    var defaultProps;\n    var propTypes; // $FlowFixMe\n\n    Object.defineProperties(lazyType, {\n      defaultProps: {\n        configurable: true,\n        get: function () {\n          return defaultProps;\n        },\n        set: function (newDefaultProps) {\n          error('React.lazy(...): It is not supported to assign `defaultProps` to ' + 'a lazy component import. Either specify them where the component ' + 'is defined, or create a wrapping component around it.');\n\n          defaultProps = newDefaultProps; // Match production behavior more closely:\n          // $FlowFixMe\n\n          Object.defineProperty(lazyType, 'defaultProps', {\n            enumerable: true\n          });\n        }\n      },\n      propTypes: {\n        configurable: true,\n        get: function () {\n          return propTypes;\n        },\n        set: function (newPropTypes) {\n          error('React.lazy(...): It is not supported to assign `propTypes` to ' + 'a lazy component import. Either specify them where the component ' + 'is defined, or create a wrapping component around it.');\n\n          propTypes = newPropTypes; // Match production behavior more closely:\n          // $FlowFixMe\n\n          Object.defineProperty(lazyType, 'propTypes', {\n            enumerable: true\n          });\n        }\n      }\n    });\n  }\n\n  return lazyType;\n}\n\nfunction forwardRef(render) {\n  {\n    if (render != null && render.$$typeof === REACT_MEMO_TYPE) {\n      error('forwardRef requires a render function but received a `memo` ' + 'component. Instead of forwardRef(memo(...)), use ' + 'memo(forwardRef(...)).');\n    } else if (typeof render !== 'function') {\n      error('forwardRef requires a render function but was given %s.', render === null ? 'null' : typeof render);\n    } else {\n      if (render.length !== 0 && render.length !== 2) {\n        error('forwardRef render functions accept exactly two parameters: props and ref. %s', render.length === 1 ? 'Did you forget to use the ref parameter?' : 'Any additional parameter will be undefined.');\n      }\n    }\n\n    if (render != null) {\n      if (render.defaultProps != null || render.propTypes != null) {\n        error('forwardRef render functions do not support propTypes or defaultProps. ' + 'Did you accidentally pass a React component?');\n      }\n    }\n  }\n\n  var elementType = {\n    $$typeof: REACT_FORWARD_REF_TYPE,\n    render: render\n  };\n\n  {\n    var ownName;\n    Object.defineProperty(elementType, 'displayName', {\n      enumerable: false,\n      configurable: true,\n      get: function () {\n        return ownName;\n      },\n      set: function (name) {\n        ownName = name; // The inner component shouldn't inherit this display name in most cases,\n        // because the component may be used elsewhere.\n        // But it's nice for anonymous functions to inherit the name,\n        // so that our component-stack generation logic will display their frames.\n        // An anonymous function generally suggests a pattern like:\n        //   React.forwardRef((props, ref) => {...});\n        // This kind of inner function is not used elsewhere so the side effect is okay.\n\n        if (!render.name && !render.displayName) {\n          render.displayName = name;\n        }\n      }\n    });\n  }\n\n  return elementType;\n}\n\nvar REACT_MODULE_REFERENCE;\n\n{\n  REACT_MODULE_REFERENCE = Symbol.for('react.module.reference');\n}\n\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_MODULE_REFERENCE || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction memo(type, compare) {\n  {\n    if (!isValidElementType(type)) {\n      error('memo: The first argument must be a component. Instead ' + 'received: %s', type === null ? 'null' : typeof type);\n    }\n  }\n\n  var elementType = {\n    $$typeof: REACT_MEMO_TYPE,\n    type: type,\n    compare: compare === undefined ? null : compare\n  };\n\n  {\n    var ownName;\n    Object.defineProperty(elementType, 'displayName', {\n      enumerable: false,\n      configurable: true,\n      get: function () {\n        return ownName;\n      },\n      set: function (name) {\n        ownName = name; // The inner component shouldn't inherit this display name in most cases,\n        // because the component may be used elsewhere.\n        // But it's nice for anonymous functions to inherit the name,\n        // so that our component-stack generation logic will display their frames.\n        // An anonymous function generally suggests a pattern like:\n        //   React.memo((props) => {...});\n        // This kind of inner function is not used elsewhere so the side effect is okay.\n\n        if (!type.name && !type.displayName) {\n          type.displayName = name;\n        }\n      }\n    });\n  }\n\n  return elementType;\n}\n\nfunction resolveDispatcher() {\n  var dispatcher = ReactCurrentDispatcher.current;\n\n  {\n    if (dispatcher === null) {\n      error('Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for' + ' one of the following reasons:\\n' + '1. You might have mismatching versions of React and the renderer (such as React DOM)\\n' + '2. You might be breaking the Rules of Hooks\\n' + '3. You might have more than one copy of React in the same app\\n' + 'See https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.');\n    }\n  } // Will result in a null access error if accessed outside render phase. We\n  // intentionally don't throw our own error because this is in a hot path.\n  // Also helps ensure this is inlined.\n\n\n  return dispatcher;\n}\nfunction useContext(Context) {\n  var dispatcher = resolveDispatcher();\n\n  {\n    // TODO: add a more generic warning for invalid values.\n    if (Context._context !== undefined) {\n      var realContext = Context._context; // Don't deduplicate because this legitimately causes bugs\n      // and nobody should be using this in existing code.\n\n      if (realContext.Consumer === Context) {\n        error('Calling useContext(Context.Consumer) is not supported, may cause bugs, and will be ' + 'removed in a future major release. Did you mean to call useContext(Context) instead?');\n      } else if (realContext.Provider === Context) {\n        error('Calling useContext(Context.Provider) is not supported. ' + 'Did you mean to call useContext(Context) instead?');\n      }\n    }\n  }\n\n  return dispatcher.useContext(Context);\n}\nfunction useState(initialState) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useState(initialState);\n}\nfunction useReducer(reducer, initialArg, init) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useReducer(reducer, initialArg, init);\n}\nfunction useRef(initialValue) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useRef(initialValue);\n}\nfunction useEffect(create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useEffect(create, deps);\n}\nfunction useInsertionEffect(create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useInsertionEffect(create, deps);\n}\nfunction useLayoutEffect(create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useLayoutEffect(create, deps);\n}\nfunction useCallback(callback, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useCallback(callback, deps);\n}\nfunction useMemo(create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useMemo(create, deps);\n}\nfunction useImperativeHandle(ref, create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useImperativeHandle(ref, create, deps);\n}\nfunction useDebugValue(value, formatterFn) {\n  {\n    var dispatcher = resolveDispatcher();\n    return dispatcher.useDebugValue(value, formatterFn);\n  }\n}\nfunction useTransition() {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useTransition();\n}\nfunction useDeferredValue(value) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useDeferredValue(value);\n}\nfunction useId() {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useId();\n}\nfunction useSyncExternalStore(subscribe, getSnapshot, getServerSnapshot) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useSyncExternalStore(subscribe, getSnapshot, getServerSnapshot);\n}\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher$1 = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, source, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if ( !fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  var control;\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher$1.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher$1.current = null;\n    disableLogs();\n  }\n\n  try {\n    // This should throw.\n    if (construct) {\n      // Something should be setting the props in the constructor.\n      var Fake = function () {\n        throw Error();\n      }; // $FlowFixMe\n\n\n      Object.defineProperty(Fake.prototype, 'props', {\n        set: function () {\n          // We use a throwing setter instead of frozen or non-writable props\n          // because that won't throw in a non-strict mode function.\n          throw Error();\n        }\n      });\n\n      if (typeof Reflect === 'object' && Reflect.construct) {\n        // We construct a different control for this case to include any extra\n        // frames added by the construct call.\n        try {\n          Reflect.construct(Fake, []);\n        } catch (x) {\n          control = x;\n        }\n\n        Reflect.construct(fn, [], Fake);\n      } else {\n        try {\n          Fake.call();\n        } catch (x) {\n          control = x;\n        }\n\n        fn.call(Fake.prototype);\n      }\n    } else {\n      try {\n        throw Error();\n      } catch (x) {\n        control = x;\n      }\n\n      fn();\n    }\n  } catch (sample) {\n    // This is inlined manually because closure doesn't do it for us.\n    if (sample && control && typeof sample.stack === 'string') {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sample.stack.split('\\n');\n      var controlLines = control.stack.split('\\n');\n      var s = sampleLines.length - 1;\n      var c = controlLines.length - 1;\n\n      while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n        // We expect at least one stack frame to be shared.\n        // Typically this will be the root most one. However, stack frames may be\n        // cut off due to maximum stack limits. In this case, one maybe cut off\n        // earlier than the other. We assume that the sample is longer or the same\n        // and there for cut off earlier. So we should find the root most frame in\n        // the sample somewhere in the control.\n        c--;\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher$1.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, source, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar loggedTypeFailures = {};\nvar ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction checkPropTypes(typeSpecs, values, location, componentName, element) {\n  {\n    // $FlowFixMe This is okay but Flow doesn't know it.\n    var has = Function.call.bind(hasOwnProperty);\n\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            // eslint-disable-next-line react-internal/prod-error-codes\n            var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n\n          error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n        } catch (ex) {\n          error$1 = ex;\n        }\n\n        if (error$1 && !(error$1 instanceof Error)) {\n          setCurrentlyValidatingElement(element);\n\n          error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n\n          setCurrentlyValidatingElement(null);\n        }\n\n        if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error$1.message] = true;\n          setCurrentlyValidatingElement(element);\n\n          error('Failed %s type: %s', location, error$1.message);\n\n          setCurrentlyValidatingElement(null);\n        }\n      }\n    }\n  }\n}\n\nfunction setCurrentlyValidatingElement$1(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      setExtraStackFrame(stack);\n    } else {\n      setExtraStackFrame(null);\n    }\n  }\n}\n\nvar propTypesMisspellWarningShown;\n\n{\n  propTypesMisspellWarningShown = false;\n}\n\nfunction getDeclarationErrorAddendum() {\n  if (ReactCurrentOwner.current) {\n    var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n    if (name) {\n      return '\\n\\nCheck the render method of `' + name + '`.';\n    }\n  }\n\n  return '';\n}\n\nfunction getSourceInfoErrorAddendum(source) {\n  if (source !== undefined) {\n    var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n    var lineNumber = source.lineNumber;\n    return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n  }\n\n  return '';\n}\n\nfunction getSourceInfoErrorAddendumForProps(elementProps) {\n  if (elementProps !== null && elementProps !== undefined) {\n    return getSourceInfoErrorAddendum(elementProps.__source);\n  }\n\n  return '';\n}\n/**\n * Warn if there's no key explicitly set on dynamic arrays of children or\n * object keys are not valid. This allows us to keep track of children between\n * updates.\n */\n\n\nvar ownerHasKeyUseWarning = {};\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  var info = getDeclarationErrorAddendum();\n\n  if (!info) {\n    var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n\n    if (parentName) {\n      info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n    }\n  }\n\n  return info;\n}\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\n\nfunction validateExplicitKey(element, parentType) {\n  if (!element._store || element._store.validated || element.key != null) {\n    return;\n  }\n\n  element._store.validated = true;\n  var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n  if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n    return;\n  }\n\n  ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n  // property, it may be the creator of the child that's responsible for\n  // assigning it a key.\n\n  var childOwner = '';\n\n  if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n    // Give the component that originally created this child.\n    childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n  }\n\n  {\n    setCurrentlyValidatingElement$1(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement$1(null);\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  if (typeof node !== 'object') {\n    return;\n  }\n\n  if (isArray(node)) {\n    for (var i = 0; i < node.length; i++) {\n      var child = node[i];\n\n      if (isValidElement(child)) {\n        validateExplicitKey(child, parentType);\n      }\n    }\n  } else if (isValidElement(node)) {\n    // This element was passed in a valid location.\n    if (node._store) {\n      node._store.validated = true;\n    }\n  } else if (node) {\n    var iteratorFn = getIteratorFn(node);\n\n    if (typeof iteratorFn === 'function') {\n      // Entry iterators used to provide implicit keys,\n      // but now we print a separate warning for them later.\n      if (iteratorFn !== node.entries) {\n        var iterator = iteratorFn.call(node);\n        var step;\n\n        while (!(step = iterator.next()).done) {\n          if (isValidElement(step.value)) {\n            validateExplicitKey(step.value, parentType);\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Given an element, validate that its props follow the propTypes definition,\n * provided by the type.\n *\n * @param {ReactElement} element\n */\n\n\nfunction validatePropTypes(element) {\n  {\n    var type = element.type;\n\n    if (type === null || type === undefined || typeof type === 'string') {\n      return;\n    }\n\n    var propTypes;\n\n    if (typeof type === 'function') {\n      propTypes = type.propTypes;\n    } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\n    // Inner props are checked in the reconciler.\n    type.$$typeof === REACT_MEMO_TYPE)) {\n      propTypes = type.propTypes;\n    } else {\n      return;\n    }\n\n    if (propTypes) {\n      // Intentionally inside to avoid triggering lazy initializers:\n      var name = getComponentNameFromType(type);\n      checkPropTypes(propTypes, element.props, 'prop', name, element);\n    } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n      propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n      var _name = getComponentNameFromType(type);\n\n      error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n    }\n\n    if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n      error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n    }\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement$1(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement$1(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement$1(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement$1(null);\n    }\n  }\n}\nfunction createElementWithValidation(type, props, children) {\n  var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n  // succeed and there will likely be errors in render.\n\n  if (!validType) {\n    var info = '';\n\n    if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n      info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n    }\n\n    var sourceInfo = getSourceInfoErrorAddendumForProps(props);\n\n    if (sourceInfo) {\n      info += sourceInfo;\n    } else {\n      info += getDeclarationErrorAddendum();\n    }\n\n    var typeString;\n\n    if (type === null) {\n      typeString = 'null';\n    } else if (isArray(type)) {\n      typeString = 'array';\n    } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n      typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n      info = ' Did you accidentally export a JSX literal instead of a component?';\n    } else {\n      typeString = typeof type;\n    }\n\n    {\n      error('React.createElement: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    }\n  }\n\n  var element = createElement.apply(this, arguments); // The result can be nullish if a mock or a custom function is used.\n  // TODO: Drop this when these are no longer allowed as the type argument.\n\n  if (element == null) {\n    return element;\n  } // Skip key warning if the type isn't valid since our key validation logic\n  // doesn't expect a non-string/function type and can throw confusing errors.\n  // We don't want exception behavior to differ between dev and prod.\n  // (Rendering will throw with a helpful message and as soon as the type is\n  // fixed, the key warnings will appear.)\n\n\n  if (validType) {\n    for (var i = 2; i < arguments.length; i++) {\n      validateChildKeys(arguments[i], type);\n    }\n  }\n\n  if (type === REACT_FRAGMENT_TYPE) {\n    validateFragmentProps(element);\n  } else {\n    validatePropTypes(element);\n  }\n\n  return element;\n}\nvar didWarnAboutDeprecatedCreateFactory = false;\nfunction createFactoryWithValidation(type) {\n  var validatedFactory = createElementWithValidation.bind(null, type);\n  validatedFactory.type = type;\n\n  {\n    if (!didWarnAboutDeprecatedCreateFactory) {\n      didWarnAboutDeprecatedCreateFactory = true;\n\n      warn('React.createFactory() is deprecated and will be removed in ' + 'a future major release. Consider using JSX ' + 'or use React.createElement() directly instead.');\n    } // Legacy hook: remove it\n\n\n    Object.defineProperty(validatedFactory, 'type', {\n      enumerable: false,\n      get: function () {\n        warn('Factory.type is deprecated. Access the class directly ' + 'before passing it to createFactory.');\n\n        Object.defineProperty(this, 'type', {\n          value: type\n        });\n        return type;\n      }\n    });\n  }\n\n  return validatedFactory;\n}\nfunction cloneElementWithValidation(element, props, children) {\n  var newElement = cloneElement.apply(this, arguments);\n\n  for (var i = 2; i < arguments.length; i++) {\n    validateChildKeys(arguments[i], newElement.type);\n  }\n\n  validatePropTypes(newElement);\n  return newElement;\n}\n\nfunction startTransition(scope, options) {\n  var prevTransition = ReactCurrentBatchConfig.transition;\n  ReactCurrentBatchConfig.transition = {};\n  var currentTransition = ReactCurrentBatchConfig.transition;\n\n  {\n    ReactCurrentBatchConfig.transition._updatedFibers = new Set();\n  }\n\n  try {\n    scope();\n  } finally {\n    ReactCurrentBatchConfig.transition = prevTransition;\n\n    {\n      if (prevTransition === null && currentTransition._updatedFibers) {\n        var updatedFibersCount = currentTransition._updatedFibers.size;\n\n        if (updatedFibersCount > 10) {\n          warn('Detected a large number of updates inside startTransition. ' + 'If this is due to a subscription please re-write it to use React provided hooks. ' + 'Otherwise concurrent mode guarantees are off the table.');\n        }\n\n        currentTransition._updatedFibers.clear();\n      }\n    }\n  }\n}\n\nvar didWarnAboutMessageChannel = false;\nvar enqueueTaskImpl = null;\nfunction enqueueTask(task) {\n  if (enqueueTaskImpl === null) {\n    try {\n      // read require off the module object to get around the bundlers.\n      // we don't want them to detect a require and bundle a Node polyfill.\n      var requireString = ('require' + Math.random()).slice(0, 7);\n      var nodeRequire = module && module[requireString]; // assuming we're in node, let's try to get node's\n      // version of setImmediate, bypassing fake timers if any.\n\n      enqueueTaskImpl = nodeRequire.call(module, 'timers').setImmediate;\n    } catch (_err) {\n      // we're in a browser\n      // we can't use regular timers because they may still be faked\n      // so we try MessageChannel+postMessage instead\n      enqueueTaskImpl = function (callback) {\n        {\n          if (didWarnAboutMessageChannel === false) {\n            didWarnAboutMessageChannel = true;\n\n            if (typeof MessageChannel === 'undefined') {\n              error('This browser does not have a MessageChannel implementation, ' + 'so enqueuing tasks via await act(async () => ...) will fail. ' + 'Please file an issue at https://github.com/facebook/react/issues ' + 'if you encounter this warning.');\n            }\n          }\n        }\n\n        var channel = new MessageChannel();\n        channel.port1.onmessage = callback;\n        channel.port2.postMessage(undefined);\n      };\n    }\n  }\n\n  return enqueueTaskImpl(task);\n}\n\nvar actScopeDepth = 0;\nvar didWarnNoAwaitAct = false;\nfunction act(callback) {\n  {\n    // `act` calls can be nested, so we track the depth. This represents the\n    // number of `act` scopes on the stack.\n    var prevActScopeDepth = actScopeDepth;\n    actScopeDepth++;\n\n    if (ReactCurrentActQueue.current === null) {\n      // This is the outermost `act` scope. Initialize the queue. The reconciler\n      // will detect the queue and use it instead of Scheduler.\n      ReactCurrentActQueue.current = [];\n    }\n\n    var prevIsBatchingLegacy = ReactCurrentActQueue.isBatchingLegacy;\n    var result;\n\n    try {\n      // Used to reproduce behavior of `batchedUpdates` in legacy mode. Only\n      // set to `true` while the given callback is executed, not for updates\n      // triggered during an async event, because this is how the legacy\n      // implementation of `act` behaved.\n      ReactCurrentActQueue.isBatchingLegacy = true;\n      result = callback(); // Replicate behavior of original `act` implementation in legacy mode,\n      // which flushed updates immediately after the scope function exits, even\n      // if it's an async function.\n\n      if (!prevIsBatchingLegacy && ReactCurrentActQueue.didScheduleLegacyUpdate) {\n        var queue = ReactCurrentActQueue.current;\n\n        if (queue !== null) {\n          ReactCurrentActQueue.didScheduleLegacyUpdate = false;\n          flushActQueue(queue);\n        }\n      }\n    } catch (error) {\n      popActScope(prevActScopeDepth);\n      throw error;\n    } finally {\n      ReactCurrentActQueue.isBatchingLegacy = prevIsBatchingLegacy;\n    }\n\n    if (result !== null && typeof result === 'object' && typeof result.then === 'function') {\n      var thenableResult = result; // The callback is an async function (i.e. returned a promise). Wait\n      // for it to resolve before exiting the current scope.\n\n      var wasAwaited = false;\n      var thenable = {\n        then: function (resolve, reject) {\n          wasAwaited = true;\n          thenableResult.then(function (returnValue) {\n            popActScope(prevActScopeDepth);\n\n            if (actScopeDepth === 0) {\n              // We've exited the outermost act scope. Recursively flush the\n              // queue until there's no remaining work.\n              recursivelyFlushAsyncActWork(returnValue, resolve, reject);\n            } else {\n              resolve(returnValue);\n            }\n          }, function (error) {\n            // The callback threw an error.\n            popActScope(prevActScopeDepth);\n            reject(error);\n          });\n        }\n      };\n\n      {\n        if (!didWarnNoAwaitAct && typeof Promise !== 'undefined') {\n          // eslint-disable-next-line no-undef\n          Promise.resolve().then(function () {}).then(function () {\n            if (!wasAwaited) {\n              didWarnNoAwaitAct = true;\n\n              error('You called act(async () => ...) without await. ' + 'This could lead to unexpected testing behaviour, ' + 'interleaving multiple act calls and mixing their ' + 'scopes. ' + 'You should - await act(async () => ...);');\n            }\n          });\n        }\n      }\n\n      return thenable;\n    } else {\n      var returnValue = result; // The callback is not an async function. Exit the current scope\n      // immediately, without awaiting.\n\n      popActScope(prevActScopeDepth);\n\n      if (actScopeDepth === 0) {\n        // Exiting the outermost act scope. Flush the queue.\n        var _queue = ReactCurrentActQueue.current;\n\n        if (_queue !== null) {\n          flushActQueue(_queue);\n          ReactCurrentActQueue.current = null;\n        } // Return a thenable. If the user awaits it, we'll flush again in\n        // case additional work was scheduled by a microtask.\n\n\n        var _thenable = {\n          then: function (resolve, reject) {\n            // Confirm we haven't re-entered another `act` scope, in case\n            // the user does something weird like await the thenable\n            // multiple times.\n            if (ReactCurrentActQueue.current === null) {\n              // Recursively flush the queue until there's no remaining work.\n              ReactCurrentActQueue.current = [];\n              recursivelyFlushAsyncActWork(returnValue, resolve, reject);\n            } else {\n              resolve(returnValue);\n            }\n          }\n        };\n        return _thenable;\n      } else {\n        // Since we're inside a nested `act` scope, the returned thenable\n        // immediately resolves. The outer scope will flush the queue.\n        var _thenable2 = {\n          then: function (resolve, reject) {\n            resolve(returnValue);\n          }\n        };\n        return _thenable2;\n      }\n    }\n  }\n}\n\nfunction popActScope(prevActScopeDepth) {\n  {\n    if (prevActScopeDepth !== actScopeDepth - 1) {\n      error('You seem to have overlapping act() calls, this is not supported. ' + 'Be sure to await previous act() calls before making a new one. ');\n    }\n\n    actScopeDepth = prevActScopeDepth;\n  }\n}\n\nfunction recursivelyFlushAsyncActWork(returnValue, resolve, reject) {\n  {\n    var queue = ReactCurrentActQueue.current;\n\n    if (queue !== null) {\n      try {\n        flushActQueue(queue);\n        enqueueTask(function () {\n          if (queue.length === 0) {\n            // No additional work was scheduled. Finish.\n            ReactCurrentActQueue.current = null;\n            resolve(returnValue);\n          } else {\n            // Keep flushing work until there's none left.\n            recursivelyFlushAsyncActWork(returnValue, resolve, reject);\n          }\n        });\n      } catch (error) {\n        reject(error);\n      }\n    } else {\n      resolve(returnValue);\n    }\n  }\n}\n\nvar isFlushing = false;\n\nfunction flushActQueue(queue) {\n  {\n    if (!isFlushing) {\n      // Prevent re-entrance.\n      isFlushing = true;\n      var i = 0;\n\n      try {\n        for (; i < queue.length; i++) {\n          var callback = queue[i];\n\n          do {\n            callback = callback(true);\n          } while (callback !== null);\n        }\n\n        queue.length = 0;\n      } catch (error) {\n        // If something throws, leave the remaining callbacks on the queue.\n        queue = queue.slice(i + 1);\n        throw error;\n      } finally {\n        isFlushing = false;\n      }\n    }\n  }\n}\n\nvar createElement$1 =  createElementWithValidation ;\nvar cloneElement$1 =  cloneElementWithValidation ;\nvar createFactory =  createFactoryWithValidation ;\nvar Children = {\n  map: mapChildren,\n  forEach: forEachChildren,\n  count: countChildren,\n  toArray: toArray,\n  only: onlyChild\n};\n\nexports.Children = Children;\nexports.Component = Component;\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.Profiler = REACT_PROFILER_TYPE;\nexports.PureComponent = PureComponent;\nexports.StrictMode = REACT_STRICT_MODE_TYPE;\nexports.Suspense = REACT_SUSPENSE_TYPE;\nexports.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = ReactSharedInternals;\nexports.act = act;\nexports.cloneElement = cloneElement$1;\nexports.createContext = createContext;\nexports.createElement = createElement$1;\nexports.createFactory = createFactory;\nexports.createRef = createRef;\nexports.forwardRef = forwardRef;\nexports.isValidElement = isValidElement;\nexports.lazy = lazy;\nexports.memo = memo;\nexports.startTransition = startTransition;\nexports.unstable_act = act;\nexports.useCallback = useCallback;\nexports.useContext = useContext;\nexports.useDebugValue = useDebugValue;\nexports.useDeferredValue = useDeferredValue;\nexports.useEffect = useEffect;\nexports.useId = useId;\nexports.useImperativeHandle = useImperativeHandle;\nexports.useInsertionEffect = useInsertionEffect;\nexports.useLayoutEffect = useLayoutEffect;\nexports.useMemo = useMemo;\nexports.useReducer = useReducer;\nexports.useRef = useRef;\nexports.useState = useState;\nexports.useSyncExternalStore = useSyncExternalStore;\nexports.useTransition = useTransition;\nexports.version = ReactVersion;\n          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n}\n        \n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react.production.min.js');\n} else {\n  module.exports = require('./cjs/react.development.js');\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAYA,QAAI,MAAuC;AACzC,OAAC,WAAW;AAEJ;AAGV,YACE,OAAO,mCAAmC,eAC1C,OAAO,+BAA+B,gCACpC,YACF;AACA,yCAA+B,4BAA4B,IAAI,MAAM,CAAC;AAAA,QACxE;AACU,YAAI,eAAe;AAM7B,YAAI,qBAAqB,OAAO,IAAI,eAAe;AACnD,YAAI,oBAAoB,OAAO,IAAI,cAAc;AACjD,YAAI,sBAAsB,OAAO,IAAI,gBAAgB;AACrD,YAAI,yBAAyB,OAAO,IAAI,mBAAmB;AAC3D,YAAI,sBAAsB,OAAO,IAAI,gBAAgB;AACrD,YAAI,sBAAsB,OAAO,IAAI,gBAAgB;AACrD,YAAI,qBAAqB,OAAO,IAAI,eAAe;AACnD,YAAI,yBAAyB,OAAO,IAAI,mBAAmB;AAC3D,YAAI,sBAAsB,OAAO,IAAI,gBAAgB;AACrD,YAAI,2BAA2B,OAAO,IAAI,qBAAqB;AAC/D,YAAI,kBAAkB,OAAO,IAAI,YAAY;AAC7C,YAAI,kBAAkB,OAAO,IAAI,YAAY;AAC7C,YAAI,uBAAuB,OAAO,IAAI,iBAAiB;AACvD,YAAI,wBAAwB,OAAO;AACnC,YAAI,uBAAuB;AAC3B,iBAAS,cAAc,eAAe;AACpC,cAAI,kBAAkB,QAAQ,OAAO,kBAAkB,UAAU;AAC/D,mBAAO;AAAA,UACT;AAEA,cAAI,gBAAgB,yBAAyB,cAAc,qBAAqB,KAAK,cAAc,oBAAoB;AAEvH,cAAI,OAAO,kBAAkB,YAAY;AACvC,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAKA,YAAI,yBAAyB;AAAA;AAAA;AAAA;AAAA;AAAA,UAK3B,SAAS;AAAA,QACX;AAMA,YAAI,0BAA0B;AAAA,UAC5B,YAAY;AAAA,QACd;AAEA,YAAI,uBAAuB;AAAA,UACzB,SAAS;AAAA;AAAA,UAET,kBAAkB;AAAA,UAClB,yBAAyB;AAAA,QAC3B;AAQA,YAAI,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA,UAKtB,SAAS;AAAA,QACX;AAEA,YAAI,yBAAyB,CAAC;AAC9B,YAAI,yBAAyB;AAC7B,iBAAS,mBAAmB,OAAO;AACjC;AACE,qCAAyB;AAAA,UAC3B;AAAA,QACF;AAEA;AACE,iCAAuB,qBAAqB,SAAU,OAAO;AAC3D;AACE,uCAAyB;AAAA,YAC3B;AAAA,UACF;AAGA,iCAAuB,kBAAkB;AAEzC,iCAAuB,mBAAmB,WAAY;AACpD,gBAAI,QAAQ;AAEZ,gBAAI,wBAAwB;AAC1B,uBAAS;AAAA,YACX;AAGA,gBAAI,OAAO,uBAAuB;AAElC,gBAAI,MAAM;AACR,uBAAS,KAAK,KAAK;AAAA,YACrB;AAEA,mBAAO;AAAA,UACT;AAAA,QACF;AAIA,YAAI,iBAAiB;AACrB,YAAI,qBAAqB;AACzB,YAAI,0BAA0B;AAE9B,YAAI,qBAAqB;AAIzB,YAAI,qBAAqB;AAEzB,YAAI,uBAAuB;AAAA,UACzB;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAEA;AACE,+BAAqB,yBAAyB;AAC9C,+BAAqB,uBAAuB;AAAA,QAC9C;AAOA,iBAAS,KAAK,QAAQ;AACpB;AACE;AACE,uBAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1G,qBAAK,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,cACjC;AAEA,2BAAa,QAAQ,QAAQ,IAAI;AAAA,YACnC;AAAA,UACF;AAAA,QACF;AACA,iBAAS,MAAM,QAAQ;AACrB;AACE;AACE,uBAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACjH,qBAAK,QAAQ,CAAC,IAAI,UAAU,KAAK;AAAA,cACnC;AAEA,2BAAa,SAAS,QAAQ,IAAI;AAAA,YACpC;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,aAAa,OAAO,QAAQ,MAAM;AAGzC;AACE,gBAAIA,0BAAyB,qBAAqB;AAClD,gBAAI,QAAQA,wBAAuB,iBAAiB;AAEpD,gBAAI,UAAU,IAAI;AAChB,wBAAU;AACV,qBAAO,KAAK,OAAO,CAAC,KAAK,CAAC;AAAA,YAC5B;AAGA,gBAAI,iBAAiB,KAAK,IAAI,SAAU,MAAM;AAC5C,qBAAO,OAAO,IAAI;AAAA,YACpB,CAAC;AAED,2BAAe,QAAQ,cAAc,MAAM;AAI3C,qBAAS,UAAU,MAAM,KAAK,QAAQ,KAAK,GAAG,SAAS,cAAc;AAAA,UACvE;AAAA,QACF;AAEA,YAAI,0CAA0C,CAAC;AAE/C,iBAAS,SAAS,gBAAgB,YAAY;AAC5C;AACE,gBAAI,eAAe,eAAe;AAClC,gBAAI,gBAAgB,iBAAiB,aAAa,eAAe,aAAa,SAAS;AACvF,gBAAI,aAAa,gBAAgB,MAAM;AAEvC,gBAAI,wCAAwC,UAAU,GAAG;AACvD;AAAA,YACF;AAEA,kBAAM,yPAAwQ,YAAY,aAAa;AAEvS,oDAAwC,UAAU,IAAI;AAAA,UACxD;AAAA,QACF;AAMA,YAAI,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQzB,WAAW,SAAU,gBAAgB;AACnC,mBAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAiBA,oBAAoB,SAAU,gBAAgB,UAAU,YAAY;AAClE,qBAAS,gBAAgB,aAAa;AAAA,UACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAeA,qBAAqB,SAAU,gBAAgB,eAAe,UAAU,YAAY;AAClF,qBAAS,gBAAgB,cAAc;AAAA,UACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAcA,iBAAiB,SAAU,gBAAgB,cAAc,UAAU,YAAY;AAC7E,qBAAS,gBAAgB,UAAU;AAAA,UACrC;AAAA,QACF;AAEA,YAAI,SAAS,OAAO;AAEpB,YAAI,cAAc,CAAC;AAEnB;AACE,iBAAO,OAAO,WAAW;AAAA,QAC3B;AAMA,iBAAS,UAAU,OAAO,SAAS,SAAS;AAC1C,eAAK,QAAQ;AACb,eAAK,UAAU;AAEf,eAAK,OAAO;AAGZ,eAAK,UAAU,WAAW;AAAA,QAC5B;AAEA,kBAAU,UAAU,mBAAmB,CAAC;AA2BxC,kBAAU,UAAU,WAAW,SAAU,cAAc,UAAU;AAC/D,cAAI,OAAO,iBAAiB,YAAY,OAAO,iBAAiB,cAAc,gBAAgB,MAAM;AAClG,kBAAM,IAAI,MAAM,uHAA4H;AAAA,UAC9I;AAEA,eAAK,QAAQ,gBAAgB,MAAM,cAAc,UAAU,UAAU;AAAA,QACvE;AAiBA,kBAAU,UAAU,cAAc,SAAU,UAAU;AACpD,eAAK,QAAQ,mBAAmB,MAAM,UAAU,aAAa;AAAA,QAC/D;AAQA;AACE,cAAI,iBAAiB;AAAA,YACnB,WAAW,CAAC,aAAa,oHAAyH;AAAA,YAClJ,cAAc,CAAC,gBAAgB,iGAAsG;AAAA,UACvI;AAEA,cAAI,2BAA2B,SAAU,YAAY,MAAM;AACzD,mBAAO,eAAe,UAAU,WAAW,YAAY;AAAA,cACrD,KAAK,WAAY;AACf,qBAAK,+DAA+D,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAEpF,uBAAO;AAAA,cACT;AAAA,YACF,CAAC;AAAA,UACH;AAEA,mBAAS,UAAU,gBAAgB;AACjC,gBAAI,eAAe,eAAe,MAAM,GAAG;AACzC,uCAAyB,QAAQ,eAAe,MAAM,CAAC;AAAA,YACzD;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,iBAAiB;AAAA,QAAC;AAE3B,uBAAe,YAAY,UAAU;AAKrC,iBAAS,cAAc,OAAO,SAAS,SAAS;AAC9C,eAAK,QAAQ;AACb,eAAK,UAAU;AAEf,eAAK,OAAO;AACZ,eAAK,UAAU,WAAW;AAAA,QAC5B;AAEA,YAAI,yBAAyB,cAAc,YAAY,IAAI,eAAe;AAC1E,+BAAuB,cAAc;AAErC,eAAO,wBAAwB,UAAU,SAAS;AAClD,+BAAuB,uBAAuB;AAG9C,iBAAS,YAAY;AACnB,cAAI,YAAY;AAAA,YACd,SAAS;AAAA,UACX;AAEA;AACE,mBAAO,KAAK,SAAS;AAAA,UACvB;AAEA,iBAAO;AAAA,QACT;AAEA,YAAI,cAAc,MAAM;AAExB,iBAAS,QAAQ,GAAG;AAClB,iBAAO,YAAY,CAAC;AAAA,QACtB;AAYA,iBAAS,SAAS,OAAO;AACvB;AAEE,gBAAI,iBAAiB,OAAO,WAAW,cAAc,OAAO;AAC5D,gBAAI,OAAO,kBAAkB,MAAM,OAAO,WAAW,KAAK,MAAM,YAAY,QAAQ;AACpF,mBAAO;AAAA,UACT;AAAA,QACF;AAGA,iBAAS,kBAAkB,OAAO;AAChC;AACE,gBAAI;AACF,iCAAmB,KAAK;AACxB,qBAAO;AAAA,YACT,SAAS,GAAG;AACV,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,mBAAmB,OAAO;AAwBjC,iBAAO,KAAK;AAAA,QACd;AACA,iBAAS,uBAAuB,OAAO;AACrC;AACE,gBAAI,kBAAkB,KAAK,GAAG;AAC5B,oBAAM,mHAAwH,SAAS,KAAK,CAAC;AAE7I,qBAAO,mBAAmB,KAAK;AAAA,YACjC;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,eAAe,WAAW,WAAW,aAAa;AACzD,cAAI,cAAc,UAAU;AAE5B,cAAI,aAAa;AACf,mBAAO;AAAA,UACT;AAEA,cAAI,eAAe,UAAU,eAAe,UAAU,QAAQ;AAC9D,iBAAO,iBAAiB,KAAK,cAAc,MAAM,eAAe,MAAM;AAAA,QACxE;AAGA,iBAAS,eAAe,MAAM;AAC5B,iBAAO,KAAK,eAAe;AAAA,QAC7B;AAGA,iBAAS,yBAAyB,MAAM;AACtC,cAAI,QAAQ,MAAM;AAEhB,mBAAO;AAAA,UACT;AAEA;AACE,gBAAI,OAAO,KAAK,QAAQ,UAAU;AAChC,oBAAM,mHAAwH;AAAA,YAChI;AAAA,UACF;AAEA,cAAI,OAAO,SAAS,YAAY;AAC9B,mBAAO,KAAK,eAAe,KAAK,QAAQ;AAAA,UAC1C;AAEA,cAAI,OAAO,SAAS,UAAU;AAC5B,mBAAO;AAAA,UACT;AAEA,kBAAQ,MAAM;AAAA,YACZ,KAAK;AACH,qBAAO;AAAA,YAET,KAAK;AACH,qBAAO;AAAA,YAET,KAAK;AACH,qBAAO;AAAA,YAET,KAAK;AACH,qBAAO;AAAA,YAET,KAAK;AACH,qBAAO;AAAA,YAET,KAAK;AACH,qBAAO;AAAA,UAEX;AAEA,cAAI,OAAO,SAAS,UAAU;AAC5B,oBAAQ,KAAK,UAAU;AAAA,cACrB,KAAK;AACH,oBAAI,UAAU;AACd,uBAAO,eAAe,OAAO,IAAI;AAAA,cAEnC,KAAK;AACH,oBAAI,WAAW;AACf,uBAAO,eAAe,SAAS,QAAQ,IAAI;AAAA,cAE7C,KAAK;AACH,uBAAO,eAAe,MAAM,KAAK,QAAQ,YAAY;AAAA,cAEvD,KAAK;AACH,oBAAI,YAAY,KAAK,eAAe;AAEpC,oBAAI,cAAc,MAAM;AACtB,yBAAO;AAAA,gBACT;AAEA,uBAAO,yBAAyB,KAAK,IAAI,KAAK;AAAA,cAEhD,KAAK,iBACH;AACE,oBAAI,gBAAgB;AACpB,oBAAI,UAAU,cAAc;AAC5B,oBAAI,OAAO,cAAc;AAEzB,oBAAI;AACF,yBAAO,yBAAyB,KAAK,OAAO,CAAC;AAAA,gBAC/C,SAAS,GAAG;AACV,yBAAO;AAAA,gBACT;AAAA,cACF;AAAA,YAGJ;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,YAAI,iBAAiB,OAAO,UAAU;AAEtC,YAAI,iBAAiB;AAAA,UACnB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,UAAU;AAAA,QACZ;AACA,YAAI,4BAA4B,4BAA4B;AAE5D;AACE,mCAAyB,CAAC;AAAA,QAC5B;AAEA,iBAAS,YAAY,QAAQ;AAC3B;AACE,gBAAI,eAAe,KAAK,QAAQ,KAAK,GAAG;AACtC,kBAAI,SAAS,OAAO,yBAAyB,QAAQ,KAAK,EAAE;AAE5D,kBAAI,UAAU,OAAO,gBAAgB;AACnC,uBAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAEA,iBAAO,OAAO,QAAQ;AAAA,QACxB;AAEA,iBAAS,YAAY,QAAQ;AAC3B;AACE,gBAAI,eAAe,KAAK,QAAQ,KAAK,GAAG;AACtC,kBAAI,SAAS,OAAO,yBAAyB,QAAQ,KAAK,EAAE;AAE5D,kBAAI,UAAU,OAAO,gBAAgB;AACnC,uBAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAEA,iBAAO,OAAO,QAAQ;AAAA,QACxB;AAEA,iBAAS,2BAA2B,OAAO,aAAa;AACtD,cAAI,wBAAwB,WAAY;AACtC;AACE,kBAAI,CAAC,4BAA4B;AAC/B,6CAA6B;AAE7B,sBAAM,6OAA4P,WAAW;AAAA,cAC/Q;AAAA,YACF;AAAA,UACF;AAEA,gCAAsB,iBAAiB;AACvC,iBAAO,eAAe,OAAO,OAAO;AAAA,YAClC,KAAK;AAAA,YACL,cAAc;AAAA,UAChB,CAAC;AAAA,QACH;AAEA,iBAAS,2BAA2B,OAAO,aAAa;AACtD,cAAI,wBAAwB,WAAY;AACtC;AACE,kBAAI,CAAC,4BAA4B;AAC/B,6CAA6B;AAE7B,sBAAM,6OAA4P,WAAW;AAAA,cAC/Q;AAAA,YACF;AAAA,UACF;AAEA,gCAAsB,iBAAiB;AACvC,iBAAO,eAAe,OAAO,OAAO;AAAA,YAClC,KAAK;AAAA,YACL,cAAc;AAAA,UAChB,CAAC;AAAA,QACH;AAEA,iBAAS,qCAAqC,QAAQ;AACpD;AACE,gBAAI,OAAO,OAAO,QAAQ,YAAY,kBAAkB,WAAW,OAAO,UAAU,kBAAkB,QAAQ,cAAc,OAAO,QAAQ;AACzI,kBAAI,gBAAgB,yBAAyB,kBAAkB,QAAQ,IAAI;AAE3E,kBAAI,CAAC,uBAAuB,aAAa,GAAG;AAC1C,sBAAM,6VAAsX,eAAe,OAAO,GAAG;AAErZ,uCAAuB,aAAa,IAAI;AAAA,cAC1C;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAuBA,YAAI,eAAe,SAAU,MAAM,KAAK,KAAK,MAAM,QAAQ,OAAO,OAAO;AACvE,cAAI,UAAU;AAAA;AAAA,YAEZ,UAAU;AAAA;AAAA,YAEV;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA;AAAA,YAEA,QAAQ;AAAA,UACV;AAEA;AAKE,oBAAQ,SAAS,CAAC;AAKlB,mBAAO,eAAe,QAAQ,QAAQ,aAAa;AAAA,cACjD,cAAc;AAAA,cACd,YAAY;AAAA,cACZ,UAAU;AAAA,cACV,OAAO;AAAA,YACT,CAAC;AAED,mBAAO,eAAe,SAAS,SAAS;AAAA,cACtC,cAAc;AAAA,cACd,YAAY;AAAA,cACZ,UAAU;AAAA,cACV,OAAO;AAAA,YACT,CAAC;AAGD,mBAAO,eAAe,SAAS,WAAW;AAAA,cACxC,cAAc;AAAA,cACd,YAAY;AAAA,cACZ,UAAU;AAAA,cACV,OAAO;AAAA,YACT,CAAC;AAED,gBAAI,OAAO,QAAQ;AACjB,qBAAO,OAAO,QAAQ,KAAK;AAC3B,qBAAO,OAAO,OAAO;AAAA,YACvB;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAMA,iBAAS,cAAc,MAAM,QAAQ,UAAU;AAC7C,cAAI;AAEJ,cAAI,QAAQ,CAAC;AACb,cAAI,MAAM;AACV,cAAI,MAAM;AACV,cAAI,OAAO;AACX,cAAI,SAAS;AAEb,cAAI,UAAU,MAAM;AAClB,gBAAI,YAAY,MAAM,GAAG;AACvB,oBAAM,OAAO;AAEb;AACE,qDAAqC,MAAM;AAAA,cAC7C;AAAA,YACF;AAEA,gBAAI,YAAY,MAAM,GAAG;AACvB;AACE,uCAAuB,OAAO,GAAG;AAAA,cACnC;AAEA,oBAAM,KAAK,OAAO;AAAA,YACpB;AAEA,mBAAO,OAAO,WAAW,SAAY,OAAO,OAAO;AACnD,qBAAS,OAAO,aAAa,SAAY,OAAO,OAAO;AAEvD,iBAAK,YAAY,QAAQ;AACvB,kBAAI,eAAe,KAAK,QAAQ,QAAQ,KAAK,CAAC,eAAe,eAAe,QAAQ,GAAG;AACrF,sBAAM,QAAQ,IAAI,OAAO,QAAQ;AAAA,cACnC;AAAA,YACF;AAAA,UACF;AAIA,cAAI,iBAAiB,UAAU,SAAS;AAExC,cAAI,mBAAmB,GAAG;AACxB,kBAAM,WAAW;AAAA,UACnB,WAAW,iBAAiB,GAAG;AAC7B,gBAAI,aAAa,MAAM,cAAc;AAErC,qBAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACvC,yBAAW,CAAC,IAAI,UAAU,IAAI,CAAC;AAAA,YACjC;AAEA;AACE,kBAAI,OAAO,QAAQ;AACjB,uBAAO,OAAO,UAAU;AAAA,cAC1B;AAAA,YACF;AAEA,kBAAM,WAAW;AAAA,UACnB;AAGA,cAAI,QAAQ,KAAK,cAAc;AAC7B,gBAAI,eAAe,KAAK;AAExB,iBAAK,YAAY,cAAc;AAC7B,kBAAI,MAAM,QAAQ,MAAM,QAAW;AACjC,sBAAM,QAAQ,IAAI,aAAa,QAAQ;AAAA,cACzC;AAAA,YACF;AAAA,UACF;AAEA;AACE,gBAAI,OAAO,KAAK;AACd,kBAAI,cAAc,OAAO,SAAS,aAAa,KAAK,eAAe,KAAK,QAAQ,YAAY;AAE5F,kBAAI,KAAK;AACP,2CAA2B,OAAO,WAAW;AAAA,cAC/C;AAEA,kBAAI,KAAK;AACP,2CAA2B,OAAO,WAAW;AAAA,cAC/C;AAAA,YACF;AAAA,UACF;AAEA,iBAAO,aAAa,MAAM,KAAK,KAAK,MAAM,QAAQ,kBAAkB,SAAS,KAAK;AAAA,QACpF;AACA,iBAAS,mBAAmB,YAAY,QAAQ;AAC9C,cAAI,aAAa,aAAa,WAAW,MAAM,QAAQ,WAAW,KAAK,WAAW,OAAO,WAAW,SAAS,WAAW,QAAQ,WAAW,KAAK;AAChJ,iBAAO;AAAA,QACT;AAMA,iBAAS,aAAa,SAAS,QAAQ,UAAU;AAC/C,cAAI,YAAY,QAAQ,YAAY,QAAW;AAC7C,kBAAM,IAAI,MAAM,mFAAmF,UAAU,GAAG;AAAA,UAClH;AAEA,cAAI;AAEJ,cAAI,QAAQ,OAAO,CAAC,GAAG,QAAQ,KAAK;AAEpC,cAAI,MAAM,QAAQ;AAClB,cAAI,MAAM,QAAQ;AAElB,cAAI,OAAO,QAAQ;AAInB,cAAI,SAAS,QAAQ;AAErB,cAAI,QAAQ,QAAQ;AAEpB,cAAI,UAAU,MAAM;AAClB,gBAAI,YAAY,MAAM,GAAG;AAEvB,oBAAM,OAAO;AACb,sBAAQ,kBAAkB;AAAA,YAC5B;AAEA,gBAAI,YAAY,MAAM,GAAG;AACvB;AACE,uCAAuB,OAAO,GAAG;AAAA,cACnC;AAEA,oBAAM,KAAK,OAAO;AAAA,YACpB;AAGA,gBAAI;AAEJ,gBAAI,QAAQ,QAAQ,QAAQ,KAAK,cAAc;AAC7C,6BAAe,QAAQ,KAAK;AAAA,YAC9B;AAEA,iBAAK,YAAY,QAAQ;AACvB,kBAAI,eAAe,KAAK,QAAQ,QAAQ,KAAK,CAAC,eAAe,eAAe,QAAQ,GAAG;AACrF,oBAAI,OAAO,QAAQ,MAAM,UAAa,iBAAiB,QAAW;AAEhE,wBAAM,QAAQ,IAAI,aAAa,QAAQ;AAAA,gBACzC,OAAO;AACL,wBAAM,QAAQ,IAAI,OAAO,QAAQ;AAAA,gBACnC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAIA,cAAI,iBAAiB,UAAU,SAAS;AAExC,cAAI,mBAAmB,GAAG;AACxB,kBAAM,WAAW;AAAA,UACnB,WAAW,iBAAiB,GAAG;AAC7B,gBAAI,aAAa,MAAM,cAAc;AAErC,qBAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACvC,yBAAW,CAAC,IAAI,UAAU,IAAI,CAAC;AAAA,YACjC;AAEA,kBAAM,WAAW;AAAA,UACnB;AAEA,iBAAO,aAAa,QAAQ,MAAM,KAAK,KAAK,MAAM,QAAQ,OAAO,KAAK;AAAA,QACxE;AASA,iBAAS,eAAe,QAAQ;AAC9B,iBAAO,OAAO,WAAW,YAAY,WAAW,QAAQ,OAAO,aAAa;AAAA,QAC9E;AAEA,YAAI,YAAY;AAChB,YAAI,eAAe;AAQnB,iBAAS,OAAO,KAAK;AACnB,cAAI,cAAc;AAClB,cAAI,gBAAgB;AAAA,YAClB,KAAK;AAAA,YACL,KAAK;AAAA,UACP;AACA,cAAI,gBAAgB,IAAI,QAAQ,aAAa,SAAU,OAAO;AAC5D,mBAAO,cAAc,KAAK;AAAA,UAC5B,CAAC;AACD,iBAAO,MAAM;AAAA,QACf;AAOA,YAAI,mBAAmB;AACvB,YAAI,6BAA6B;AAEjC,iBAAS,sBAAsB,MAAM;AACnC,iBAAO,KAAK,QAAQ,4BAA4B,KAAK;AAAA,QACvD;AAUA,iBAAS,cAAc,SAAS,OAAO;AAGrC,cAAI,OAAO,YAAY,YAAY,YAAY,QAAQ,QAAQ,OAAO,MAAM;AAE1E;AACE,qCAAuB,QAAQ,GAAG;AAAA,YACpC;AAEA,mBAAO,OAAO,KAAK,QAAQ,GAAG;AAAA,UAChC;AAGA,iBAAO,MAAM,SAAS,EAAE;AAAA,QAC1B;AAEA,iBAAS,aAAa,UAAU,OAAO,eAAe,WAAW,UAAU;AACzE,cAAI,OAAO,OAAO;AAElB,cAAI,SAAS,eAAe,SAAS,WAAW;AAE9C,uBAAW;AAAA,UACb;AAEA,cAAI,iBAAiB;AAErB,cAAI,aAAa,MAAM;AACrB,6BAAiB;AAAA,UACnB,OAAO;AACL,oBAAQ,MAAM;AAAA,cACZ,KAAK;AAAA,cACL,KAAK;AACH,iCAAiB;AACjB;AAAA,cAEF,KAAK;AACH,wBAAQ,SAAS,UAAU;AAAA,kBACzB,KAAK;AAAA,kBACL,KAAK;AACH,qCAAiB;AAAA,gBACrB;AAAA,YAEJ;AAAA,UACF;AAEA,cAAI,gBAAgB;AAClB,gBAAI,SAAS;AACb,gBAAI,cAAc,SAAS,MAAM;AAGjC,gBAAI,WAAW,cAAc,KAAK,YAAY,cAAc,QAAQ,CAAC,IAAI;AAEzE,gBAAI,QAAQ,WAAW,GAAG;AACxB,kBAAI,kBAAkB;AAEtB,kBAAI,YAAY,MAAM;AACpB,kCAAkB,sBAAsB,QAAQ,IAAI;AAAA,cACtD;AAEA,2BAAa,aAAa,OAAO,iBAAiB,IAAI,SAAU,GAAG;AACjE,uBAAO;AAAA,cACT,CAAC;AAAA,YACH,WAAW,eAAe,MAAM;AAC9B,kBAAI,eAAe,WAAW,GAAG;AAC/B;AAIE,sBAAI,YAAY,QAAQ,CAAC,UAAU,OAAO,QAAQ,YAAY,MAAM;AAClE,2CAAuB,YAAY,GAAG;AAAA,kBACxC;AAAA,gBACF;AAEA,8BAAc;AAAA,kBAAmB;AAAA;AAAA;AAAA,kBAEjC;AAAA,mBACA,YAAY,QAAQ,CAAC,UAAU,OAAO,QAAQ,YAAY;AAAA;AAAA;AAAA,oBAE1D,sBAAsB,KAAK,YAAY,GAAG,IAAI;AAAA,sBAAM,MAAM;AAAA,gBAAQ;AAAA,cACpE;AAEA,oBAAM,KAAK,WAAW;AAAA,YACxB;AAEA,mBAAO;AAAA,UACT;AAEA,cAAI;AACJ,cAAI;AACJ,cAAI,eAAe;AAEnB,cAAI,iBAAiB,cAAc,KAAK,YAAY,YAAY;AAEhE,cAAI,QAAQ,QAAQ,GAAG;AACrB,qBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,sBAAQ,SAAS,CAAC;AAClB,yBAAW,iBAAiB,cAAc,OAAO,CAAC;AAClD,8BAAgB,aAAa,OAAO,OAAO,eAAe,UAAU,QAAQ;AAAA,YAC9E;AAAA,UACF,OAAO;AACL,gBAAI,aAAa,cAAc,QAAQ;AAEvC,gBAAI,OAAO,eAAe,YAAY;AACpC,kBAAI,mBAAmB;AAEvB;AAEE,oBAAI,eAAe,iBAAiB,SAAS;AAC3C,sBAAI,CAAC,kBAAkB;AACrB,yBAAK,uFAA4F;AAAA,kBACnG;AAEA,qCAAmB;AAAA,gBACrB;AAAA,cACF;AAEA,kBAAI,WAAW,WAAW,KAAK,gBAAgB;AAC/C,kBAAI;AACJ,kBAAI,KAAK;AAET,qBAAO,EAAE,OAAO,SAAS,KAAK,GAAG,MAAM;AACrC,wBAAQ,KAAK;AACb,2BAAW,iBAAiB,cAAc,OAAO,IAAI;AACrD,gCAAgB,aAAa,OAAO,OAAO,eAAe,UAAU,QAAQ;AAAA,cAC9E;AAAA,YACF,WAAW,SAAS,UAAU;AAE5B,kBAAI,iBAAiB,OAAO,QAAQ;AACpC,oBAAM,IAAI,MAAM,qDAAqD,mBAAmB,oBAAoB,uBAAuB,OAAO,KAAK,QAAQ,EAAE,KAAK,IAAI,IAAI,MAAM,kBAAkB,2EAAqF;AAAA,YACrR;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAeA,iBAAS,YAAY,UAAU,MAAM,SAAS;AAC5C,cAAI,YAAY,MAAM;AACpB,mBAAO;AAAA,UACT;AAEA,cAAI,SAAS,CAAC;AACd,cAAI,QAAQ;AACZ,uBAAa,UAAU,QAAQ,IAAI,IAAI,SAAU,OAAO;AACtD,mBAAO,KAAK,KAAK,SAAS,OAAO,OAAO;AAAA,UAC1C,CAAC;AACD,iBAAO;AAAA,QACT;AAYA,iBAAS,cAAc,UAAU;AAC/B,cAAI,IAAI;AACR,sBAAY,UAAU,WAAY;AAChC;AAAA,UACF,CAAC;AACD,iBAAO;AAAA,QACT;AAcA,iBAAS,gBAAgB,UAAU,aAAa,gBAAgB;AAC9D,sBAAY,UAAU,WAAY;AAChC,wBAAY,MAAM,MAAM,SAAS;AAAA,UACnC,GAAG,cAAc;AAAA,QACnB;AASA,iBAAS,QAAQ,UAAU;AACzB,iBAAO,YAAY,UAAU,SAAU,OAAO;AAC5C,mBAAO;AAAA,UACT,CAAC,KAAK,CAAC;AAAA,QACT;AAiBA,iBAAS,UAAU,UAAU;AAC3B,cAAI,CAAC,eAAe,QAAQ,GAAG;AAC7B,kBAAM,IAAI,MAAM,uEAAuE;AAAA,UACzF;AAEA,iBAAO;AAAA,QACT;AAEA,iBAAS,cAAc,cAAc;AAGnC,cAAI,UAAU;AAAA,YACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAMV,eAAe;AAAA,YACf,gBAAgB;AAAA;AAAA;AAAA,YAGhB,cAAc;AAAA;AAAA,YAEd,UAAU;AAAA,YACV,UAAU;AAAA;AAAA,YAEV,eAAe;AAAA,YACf,aAAa;AAAA,UACf;AACA,kBAAQ,WAAW;AAAA,YACjB,UAAU;AAAA,YACV,UAAU;AAAA,UACZ;AACA,cAAI,4CAA4C;AAChD,cAAI,sCAAsC;AAC1C,cAAI,sCAAsC;AAE1C;AAIE,gBAAI,WAAW;AAAA,cACb,UAAU;AAAA,cACV,UAAU;AAAA,YACZ;AAEA,mBAAO,iBAAiB,UAAU;AAAA,cAChC,UAAU;AAAA,gBACR,KAAK,WAAY;AACf,sBAAI,CAAC,qCAAqC;AACxC,0DAAsC;AAEtC,0BAAM,0JAA+J;AAAA,kBACvK;AAEA,yBAAO,QAAQ;AAAA,gBACjB;AAAA,gBACA,KAAK,SAAU,WAAW;AACxB,0BAAQ,WAAW;AAAA,gBACrB;AAAA,cACF;AAAA,cACA,eAAe;AAAA,gBACb,KAAK,WAAY;AACf,yBAAO,QAAQ;AAAA,gBACjB;AAAA,gBACA,KAAK,SAAU,eAAe;AAC5B,0BAAQ,gBAAgB;AAAA,gBAC1B;AAAA,cACF;AAAA,cACA,gBAAgB;AAAA,gBACd,KAAK,WAAY;AACf,yBAAO,QAAQ;AAAA,gBACjB;AAAA,gBACA,KAAK,SAAU,gBAAgB;AAC7B,0BAAQ,iBAAiB;AAAA,gBAC3B;AAAA,cACF;AAAA,cACA,cAAc;AAAA,gBACZ,KAAK,WAAY;AACf,yBAAO,QAAQ;AAAA,gBACjB;AAAA,gBACA,KAAK,SAAU,cAAc;AAC3B,0BAAQ,eAAe;AAAA,gBACzB;AAAA,cACF;AAAA,cACA,UAAU;AAAA,gBACR,KAAK,WAAY;AACf,sBAAI,CAAC,2CAA2C;AAC9C,gEAA4C;AAE5C,0BAAM,0JAA+J;AAAA,kBACvK;AAEA,yBAAO,QAAQ;AAAA,gBACjB;AAAA,cACF;AAAA,cACA,aAAa;AAAA,gBACX,KAAK,WAAY;AACf,yBAAO,QAAQ;AAAA,gBACjB;AAAA,gBACA,KAAK,SAAU,aAAa;AAC1B,sBAAI,CAAC,qCAAqC;AACxC,yBAAK,uIAA4I,WAAW;AAE5J,0DAAsC;AAAA,kBACxC;AAAA,gBACF;AAAA,cACF;AAAA,YACF,CAAC;AAED,oBAAQ,WAAW;AAAA,UACrB;AAEA;AACE,oBAAQ,mBAAmB;AAC3B,oBAAQ,oBAAoB;AAAA,UAC9B;AAEA,iBAAO;AAAA,QACT;AAEA,YAAI,gBAAgB;AACpB,YAAI,UAAU;AACd,YAAI,WAAW;AACf,YAAI,WAAW;AAEf,iBAAS,gBAAgB,SAAS;AAChC,cAAI,QAAQ,YAAY,eAAe;AACrC,gBAAI,OAAO,QAAQ;AACnB,gBAAI,WAAW,KAAK;AAMpB,qBAAS,KAAK,SAAUC,eAAc;AACpC,kBAAI,QAAQ,YAAY,WAAW,QAAQ,YAAY,eAAe;AAEpE,oBAAI,WAAW;AACf,yBAAS,UAAU;AACnB,yBAAS,UAAUA;AAAA,cACrB;AAAA,YACF,GAAG,SAAUC,QAAO;AAClB,kBAAI,QAAQ,YAAY,WAAW,QAAQ,YAAY,eAAe;AAEpE,oBAAI,WAAW;AACf,yBAAS,UAAU;AACnB,yBAAS,UAAUA;AAAA,cACrB;AAAA,YACF,CAAC;AAED,gBAAI,QAAQ,YAAY,eAAe;AAGrC,kBAAI,UAAU;AACd,sBAAQ,UAAU;AAClB,sBAAQ,UAAU;AAAA,YACpB;AAAA,UACF;AAEA,cAAI,QAAQ,YAAY,UAAU;AAChC,gBAAI,eAAe,QAAQ;AAE3B;AACE,kBAAI,iBAAiB,QAAW;AAC9B,sBAAM,qOAC2H,YAAY;AAAA,cAC/I;AAAA,YACF;AAEA;AACE,kBAAI,EAAE,aAAa,eAAe;AAChC,sBAAM,yKAC0D,YAAY;AAAA,cAC9E;AAAA,YACF;AAEA,mBAAO,aAAa;AAAA,UACtB,OAAO;AACL,kBAAM,QAAQ;AAAA,UAChB;AAAA,QACF;AAEA,iBAAS,KAAK,MAAM;AAClB,cAAI,UAAU;AAAA;AAAA,YAEZ,SAAS;AAAA,YACT,SAAS;AAAA,UACX;AACA,cAAI,WAAW;AAAA,YACb,UAAU;AAAA,YACV,UAAU;AAAA,YACV,OAAO;AAAA,UACT;AAEA;AAEE,gBAAI;AACJ,gBAAI;AAEJ,mBAAO,iBAAiB,UAAU;AAAA,cAChC,cAAc;AAAA,gBACZ,cAAc;AAAA,gBACd,KAAK,WAAY;AACf,yBAAO;AAAA,gBACT;AAAA,gBACA,KAAK,SAAU,iBAAiB;AAC9B,wBAAM,yLAAmM;AAEzM,iCAAe;AAGf,yBAAO,eAAe,UAAU,gBAAgB;AAAA,oBAC9C,YAAY;AAAA,kBACd,CAAC;AAAA,gBACH;AAAA,cACF;AAAA,cACA,WAAW;AAAA,gBACT,cAAc;AAAA,gBACd,KAAK,WAAY;AACf,yBAAO;AAAA,gBACT;AAAA,gBACA,KAAK,SAAU,cAAc;AAC3B,wBAAM,sLAAgM;AAEtM,8BAAY;AAGZ,yBAAO,eAAe,UAAU,aAAa;AAAA,oBAC3C,YAAY;AAAA,kBACd,CAAC;AAAA,gBACH;AAAA,cACF;AAAA,YACF,CAAC;AAAA,UACH;AAEA,iBAAO;AAAA,QACT;AAEA,iBAAS,WAAW,QAAQ;AAC1B;AACE,gBAAI,UAAU,QAAQ,OAAO,aAAa,iBAAiB;AACzD,oBAAM,qIAA+I;AAAA,YACvJ,WAAW,OAAO,WAAW,YAAY;AACvC,oBAAM,2DAA2D,WAAW,OAAO,SAAS,OAAO,MAAM;AAAA,YAC3G,OAAO;AACL,kBAAI,OAAO,WAAW,KAAK,OAAO,WAAW,GAAG;AAC9C,sBAAM,gFAAgF,OAAO,WAAW,IAAI,6CAA6C,6CAA6C;AAAA,cACxM;AAAA,YACF;AAEA,gBAAI,UAAU,MAAM;AAClB,kBAAI,OAAO,gBAAgB,QAAQ,OAAO,aAAa,MAAM;AAC3D,sBAAM,oHAAyH;AAAA,cACjI;AAAA,YACF;AAAA,UACF;AAEA,cAAI,cAAc;AAAA,YAChB,UAAU;AAAA,YACV;AAAA,UACF;AAEA;AACE,gBAAI;AACJ,mBAAO,eAAe,aAAa,eAAe;AAAA,cAChD,YAAY;AAAA,cACZ,cAAc;AAAA,cACd,KAAK,WAAY;AACf,uBAAO;AAAA,cACT;AAAA,cACA,KAAK,SAAU,MAAM;AACnB,0BAAU;AAQV,oBAAI,CAAC,OAAO,QAAQ,CAAC,OAAO,aAAa;AACvC,yBAAO,cAAc;AAAA,gBACvB;AAAA,cACF;AAAA,YACF,CAAC;AAAA,UACH;AAEA,iBAAO;AAAA,QACT;AAEA,YAAI;AAEJ;AACE,mCAAyB,OAAO,IAAI,wBAAwB;AAAA,QAC9D;AAEA,iBAAS,mBAAmB,MAAM;AAChC,cAAI,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAC1D,mBAAO;AAAA,UACT;AAGA,cAAI,SAAS,uBAAuB,SAAS,uBAAuB,sBAAuB,SAAS,0BAA0B,SAAS,uBAAuB,SAAS,4BAA4B,sBAAuB,SAAS,wBAAwB,kBAAmB,sBAAuB,yBAA0B;AAC7T,mBAAO;AAAA,UACT;AAEA,cAAI,OAAO,SAAS,YAAY,SAAS,MAAM;AAC7C,gBAAI,KAAK,aAAa,mBAAmB,KAAK,aAAa,mBAAmB,KAAK,aAAa,uBAAuB,KAAK,aAAa,sBAAsB,KAAK,aAAa;AAAA;AAAA;AAAA;AAAA,YAIjL,KAAK,aAAa,0BAA0B,KAAK,gBAAgB,QAAW;AAC1E,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,iBAAS,KAAK,MAAM,SAAS;AAC3B;AACE,gBAAI,CAAC,mBAAmB,IAAI,GAAG;AAC7B,oBAAM,sEAA2E,SAAS,OAAO,SAAS,OAAO,IAAI;AAAA,YACvH;AAAA,UACF;AAEA,cAAI,cAAc;AAAA,YAChB,UAAU;AAAA,YACV;AAAA,YACA,SAAS,YAAY,SAAY,OAAO;AAAA,UAC1C;AAEA;AACE,gBAAI;AACJ,mBAAO,eAAe,aAAa,eAAe;AAAA,cAChD,YAAY;AAAA,cACZ,cAAc;AAAA,cACd,KAAK,WAAY;AACf,uBAAO;AAAA,cACT;AAAA,cACA,KAAK,SAAU,MAAM;AACnB,0BAAU;AAQV,oBAAI,CAAC,KAAK,QAAQ,CAAC,KAAK,aAAa;AACnC,uBAAK,cAAc;AAAA,gBACrB;AAAA,cACF;AAAA,YACF,CAAC;AAAA,UACH;AAEA,iBAAO;AAAA,QACT;AAEA,iBAAS,oBAAoB;AAC3B,cAAI,aAAa,uBAAuB;AAExC;AACE,gBAAI,eAAe,MAAM;AACvB,oBAAM,ibAA0c;AAAA,YACld;AAAA,UACF;AAKA,iBAAO;AAAA,QACT;AACA,iBAAS,WAAW,SAAS;AAC3B,cAAI,aAAa,kBAAkB;AAEnC;AAEE,gBAAI,QAAQ,aAAa,QAAW;AAClC,kBAAI,cAAc,QAAQ;AAG1B,kBAAI,YAAY,aAAa,SAAS;AACpC,sBAAM,yKAA8K;AAAA,cACtL,WAAW,YAAY,aAAa,SAAS;AAC3C,sBAAM,0GAA+G;AAAA,cACvH;AAAA,YACF;AAAA,UACF;AAEA,iBAAO,WAAW,WAAW,OAAO;AAAA,QACtC;AACA,iBAAS,SAAS,cAAc;AAC9B,cAAI,aAAa,kBAAkB;AACnC,iBAAO,WAAW,SAAS,YAAY;AAAA,QACzC;AACA,iBAAS,WAAW,SAAS,YAAY,MAAM;AAC7C,cAAI,aAAa,kBAAkB;AACnC,iBAAO,WAAW,WAAW,SAAS,YAAY,IAAI;AAAA,QACxD;AACA,iBAAS,OAAO,cAAc;AAC5B,cAAI,aAAa,kBAAkB;AACnC,iBAAO,WAAW,OAAO,YAAY;AAAA,QACvC;AACA,iBAAS,UAAU,QAAQ,MAAM;AAC/B,cAAI,aAAa,kBAAkB;AACnC,iBAAO,WAAW,UAAU,QAAQ,IAAI;AAAA,QAC1C;AACA,iBAAS,mBAAmB,QAAQ,MAAM;AACxC,cAAI,aAAa,kBAAkB;AACnC,iBAAO,WAAW,mBAAmB,QAAQ,IAAI;AAAA,QACnD;AACA,iBAAS,gBAAgB,QAAQ,MAAM;AACrC,cAAI,aAAa,kBAAkB;AACnC,iBAAO,WAAW,gBAAgB,QAAQ,IAAI;AAAA,QAChD;AACA,iBAAS,YAAY,UAAU,MAAM;AACnC,cAAI,aAAa,kBAAkB;AACnC,iBAAO,WAAW,YAAY,UAAU,IAAI;AAAA,QAC9C;AACA,iBAAS,QAAQ,QAAQ,MAAM;AAC7B,cAAI,aAAa,kBAAkB;AACnC,iBAAO,WAAW,QAAQ,QAAQ,IAAI;AAAA,QACxC;AACA,iBAAS,oBAAoB,KAAK,QAAQ,MAAM;AAC9C,cAAI,aAAa,kBAAkB;AACnC,iBAAO,WAAW,oBAAoB,KAAK,QAAQ,IAAI;AAAA,QACzD;AACA,iBAAS,cAAc,OAAO,aAAa;AACzC;AACE,gBAAI,aAAa,kBAAkB;AACnC,mBAAO,WAAW,cAAc,OAAO,WAAW;AAAA,UACpD;AAAA,QACF;AACA,iBAAS,gBAAgB;AACvB,cAAI,aAAa,kBAAkB;AACnC,iBAAO,WAAW,cAAc;AAAA,QAClC;AACA,iBAAS,iBAAiB,OAAO;AAC/B,cAAI,aAAa,kBAAkB;AACnC,iBAAO,WAAW,iBAAiB,KAAK;AAAA,QAC1C;AACA,iBAAS,QAAQ;AACf,cAAI,aAAa,kBAAkB;AACnC,iBAAO,WAAW,MAAM;AAAA,QAC1B;AACA,iBAAS,qBAAqB,WAAW,aAAa,mBAAmB;AACvE,cAAI,aAAa,kBAAkB;AACnC,iBAAO,WAAW,qBAAqB,WAAW,aAAa,iBAAiB;AAAA,QAClF;AAMA,YAAI,gBAAgB;AACpB,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI;AAEJ,iBAAS,cAAc;AAAA,QAAC;AAExB,oBAAY,qBAAqB;AACjC,iBAAS,cAAc;AACrB;AACE,gBAAI,kBAAkB,GAAG;AAEvB,wBAAU,QAAQ;AAClB,yBAAW,QAAQ;AACnB,yBAAW,QAAQ;AACnB,0BAAY,QAAQ;AACpB,0BAAY,QAAQ;AACpB,mCAAqB,QAAQ;AAC7B,6BAAe,QAAQ;AAEvB,kBAAI,QAAQ;AAAA,gBACV,cAAc;AAAA,gBACd,YAAY;AAAA,gBACZ,OAAO;AAAA,gBACP,UAAU;AAAA,cACZ;AAEA,qBAAO,iBAAiB,SAAS;AAAA,gBAC/B,MAAM;AAAA,gBACN,KAAK;AAAA,gBACL,MAAM;AAAA,gBACN,OAAO;AAAA,gBACP,OAAO;AAAA,gBACP,gBAAgB;AAAA,gBAChB,UAAU;AAAA,cACZ,CAAC;AAAA,YAEH;AAEA;AAAA,UACF;AAAA,QACF;AACA,iBAAS,eAAe;AACtB;AACE;AAEA,gBAAI,kBAAkB,GAAG;AAEvB,kBAAI,QAAQ;AAAA,gBACV,cAAc;AAAA,gBACd,YAAY;AAAA,gBACZ,UAAU;AAAA,cACZ;AAEA,qBAAO,iBAAiB,SAAS;AAAA,gBAC/B,KAAK,OAAO,CAAC,GAAG,OAAO;AAAA,kBACrB,OAAO;AAAA,gBACT,CAAC;AAAA,gBACD,MAAM,OAAO,CAAC,GAAG,OAAO;AAAA,kBACtB,OAAO;AAAA,gBACT,CAAC;AAAA,gBACD,MAAM,OAAO,CAAC,GAAG,OAAO;AAAA,kBACtB,OAAO;AAAA,gBACT,CAAC;AAAA,gBACD,OAAO,OAAO,CAAC,GAAG,OAAO;AAAA,kBACvB,OAAO;AAAA,gBACT,CAAC;AAAA,gBACD,OAAO,OAAO,CAAC,GAAG,OAAO;AAAA,kBACvB,OAAO;AAAA,gBACT,CAAC;AAAA,gBACD,gBAAgB,OAAO,CAAC,GAAG,OAAO;AAAA,kBAChC,OAAO;AAAA,gBACT,CAAC;AAAA,gBACD,UAAU,OAAO,CAAC,GAAG,OAAO;AAAA,kBAC1B,OAAO;AAAA,gBACT,CAAC;AAAA,cACH,CAAC;AAAA,YAEH;AAEA,gBAAI,gBAAgB,GAAG;AACrB,oBAAM,8EAAmF;AAAA,YAC3F;AAAA,UACF;AAAA,QACF;AAEA,YAAI,2BAA2B,qBAAqB;AACpD,YAAI;AACJ,iBAAS,8BAA8B,MAAM,QAAQ,SAAS;AAC5D;AACE,gBAAI,WAAW,QAAW;AAExB,kBAAI;AACF,sBAAM,MAAM;AAAA,cACd,SAAS,GAAG;AACV,oBAAI,QAAQ,EAAE,MAAM,KAAK,EAAE,MAAM,cAAc;AAC/C,yBAAS,SAAS,MAAM,CAAC,KAAK;AAAA,cAChC;AAAA,YACF;AAGA,mBAAO,OAAO,SAAS;AAAA,UACzB;AAAA,QACF;AACA,YAAI,UAAU;AACd,YAAI;AAEJ;AACE,cAAI,kBAAkB,OAAO,YAAY,aAAa,UAAU;AAChE,gCAAsB,IAAI,gBAAgB;AAAA,QAC5C;AAEA,iBAAS,6BAA6B,IAAI,WAAW;AAEnD,cAAK,CAAC,MAAM,SAAS;AACnB,mBAAO;AAAA,UACT;AAEA;AACE,gBAAI,QAAQ,oBAAoB,IAAI,EAAE;AAEtC,gBAAI,UAAU,QAAW;AACvB,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,cAAI;AACJ,oBAAU;AACV,cAAI,4BAA4B,MAAM;AAEtC,gBAAM,oBAAoB;AAC1B,cAAI;AAEJ;AACE,iCAAqB,yBAAyB;AAG9C,qCAAyB,UAAU;AACnC,wBAAY;AAAA,UACd;AAEA,cAAI;AAEF,gBAAI,WAAW;AAEb,kBAAI,OAAO,WAAY;AACrB,sBAAM,MAAM;AAAA,cACd;AAGA,qBAAO,eAAe,KAAK,WAAW,SAAS;AAAA,gBAC7C,KAAK,WAAY;AAGf,wBAAM,MAAM;AAAA,gBACd;AAAA,cACF,CAAC;AAED,kBAAI,OAAO,YAAY,YAAY,QAAQ,WAAW;AAGpD,oBAAI;AACF,0BAAQ,UAAU,MAAM,CAAC,CAAC;AAAA,gBAC5B,SAAS,GAAG;AACV,4BAAU;AAAA,gBACZ;AAEA,wBAAQ,UAAU,IAAI,CAAC,GAAG,IAAI;AAAA,cAChC,OAAO;AACL,oBAAI;AACF,uBAAK,KAAK;AAAA,gBACZ,SAAS,GAAG;AACV,4BAAU;AAAA,gBACZ;AAEA,mBAAG,KAAK,KAAK,SAAS;AAAA,cACxB;AAAA,YACF,OAAO;AACL,kBAAI;AACF,sBAAM,MAAM;AAAA,cACd,SAAS,GAAG;AACV,0BAAU;AAAA,cACZ;AAEA,iBAAG;AAAA,YACL;AAAA,UACF,SAAS,QAAQ;AAEf,gBAAI,UAAU,WAAW,OAAO,OAAO,UAAU,UAAU;AAGzD,kBAAI,cAAc,OAAO,MAAM,MAAM,IAAI;AACzC,kBAAI,eAAe,QAAQ,MAAM,MAAM,IAAI;AAC3C,kBAAI,IAAI,YAAY,SAAS;AAC7B,kBAAI,IAAI,aAAa,SAAS;AAE9B,qBAAO,KAAK,KAAK,KAAK,KAAK,YAAY,CAAC,MAAM,aAAa,CAAC,GAAG;AAO7D;AAAA,cACF;AAEA,qBAAO,KAAK,KAAK,KAAK,GAAG,KAAK,KAAK;AAGjC,oBAAI,YAAY,CAAC,MAAM,aAAa,CAAC,GAAG;AAMtC,sBAAI,MAAM,KAAK,MAAM,GAAG;AACtB,uBAAG;AACD;AACA;AAGA,0BAAI,IAAI,KAAK,YAAY,CAAC,MAAM,aAAa,CAAC,GAAG;AAE/C,4BAAI,SAAS,OAAO,YAAY,CAAC,EAAE,QAAQ,YAAY,MAAM;AAK7D,4BAAI,GAAG,eAAe,OAAO,SAAS,aAAa,GAAG;AACpD,mCAAS,OAAO,QAAQ,eAAe,GAAG,WAAW;AAAA,wBACvD;AAEA;AACE,8BAAI,OAAO,OAAO,YAAY;AAC5B,gDAAoB,IAAI,IAAI,MAAM;AAAA,0BACpC;AAAA,wBACF;AAGA,+BAAO;AAAA,sBACT;AAAA,oBACF,SAAS,KAAK,KAAK,KAAK;AAAA,kBAC1B;AAEA;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF,UAAE;AACA,sBAAU;AAEV;AACE,uCAAyB,UAAU;AACnC,2BAAa;AAAA,YACf;AAEA,kBAAM,oBAAoB;AAAA,UAC5B;AAGA,cAAI,OAAO,KAAK,GAAG,eAAe,GAAG,OAAO;AAC5C,cAAI,iBAAiB,OAAO,8BAA8B,IAAI,IAAI;AAElE;AACE,gBAAI,OAAO,OAAO,YAAY;AAC5B,kCAAoB,IAAI,IAAI,cAAc;AAAA,YAC5C;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AACA,iBAAS,+BAA+B,IAAI,QAAQ,SAAS;AAC3D;AACE,mBAAO,6BAA6B,IAAI,KAAK;AAAA,UAC/C;AAAA,QACF;AAEA,iBAAS,gBAAgBC,YAAW;AAClC,cAAI,YAAYA,WAAU;AAC1B,iBAAO,CAAC,EAAE,aAAa,UAAU;AAAA,QACnC;AAEA,iBAAS,qCAAqC,MAAM,QAAQ,SAAS;AAEnE,cAAI,QAAQ,MAAM;AAChB,mBAAO;AAAA,UACT;AAEA,cAAI,OAAO,SAAS,YAAY;AAC9B;AACE,qBAAO,6BAA6B,MAAM,gBAAgB,IAAI,CAAC;AAAA,YACjE;AAAA,UACF;AAEA,cAAI,OAAO,SAAS,UAAU;AAC5B,mBAAO,8BAA8B,IAAI;AAAA,UAC3C;AAEA,kBAAQ,MAAM;AAAA,YACZ,KAAK;AACH,qBAAO,8BAA8B,UAAU;AAAA,YAEjD,KAAK;AACH,qBAAO,8BAA8B,cAAc;AAAA,UACvD;AAEA,cAAI,OAAO,SAAS,UAAU;AAC5B,oBAAQ,KAAK,UAAU;AAAA,cACrB,KAAK;AACH,uBAAO,+BAA+B,KAAK,MAAM;AAAA,cAEnD,KAAK;AAEH,uBAAO,qCAAqC,KAAK,MAAM,QAAQ,OAAO;AAAA,cAExE,KAAK,iBACH;AACE,oBAAI,gBAAgB;AACpB,oBAAI,UAAU,cAAc;AAC5B,oBAAI,OAAO,cAAc;AAEzB,oBAAI;AAEF,yBAAO,qCAAqC,KAAK,OAAO,GAAG,QAAQ,OAAO;AAAA,gBAC5E,SAAS,GAAG;AAAA,gBAAC;AAAA,cACf;AAAA,YACJ;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,YAAI,qBAAqB,CAAC;AAC1B,YAAI,2BAA2B,qBAAqB;AAEpD,iBAAS,8BAA8B,SAAS;AAC9C;AACE,gBAAI,SAAS;AACX,kBAAI,QAAQ,QAAQ;AACpB,kBAAI,QAAQ,qCAAqC,QAAQ,MAAM,QAAQ,SAAS,QAAQ,MAAM,OAAO,IAAI;AACzG,uCAAyB,mBAAmB,KAAK;AAAA,YACnD,OAAO;AACL,uCAAyB,mBAAmB,IAAI;AAAA,YAClD;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,eAAe,WAAW,QAAQ,UAAU,eAAe,SAAS;AAC3E;AAEE,gBAAI,MAAM,SAAS,KAAK,KAAK,cAAc;AAE3C,qBAAS,gBAAgB,WAAW;AAClC,kBAAI,IAAI,WAAW,YAAY,GAAG;AAChC,oBAAI,UAAU;AAId,oBAAI;AAGF,sBAAI,OAAO,UAAU,YAAY,MAAM,YAAY;AAEjD,wBAAI,MAAM,OAAO,iBAAiB,iBAAiB,OAAO,WAAW,YAAY,eAAe,+FAAoG,OAAO,UAAU,YAAY,IAAI,iGAAsG;AAC3U,wBAAI,OAAO;AACX,0BAAM;AAAA,kBACR;AAEA,4BAAU,UAAU,YAAY,EAAE,QAAQ,cAAc,eAAe,UAAU,MAAM,8CAA8C;AAAA,gBACvI,SAAS,IAAI;AACX,4BAAU;AAAA,gBACZ;AAEA,oBAAI,WAAW,EAAE,mBAAmB,QAAQ;AAC1C,gDAA8B,OAAO;AAErC,wBAAM,4RAAqT,iBAAiB,eAAe,UAAU,cAAc,OAAO,OAAO;AAEjY,gDAA8B,IAAI;AAAA,gBACpC;AAEA,oBAAI,mBAAmB,SAAS,EAAE,QAAQ,WAAW,qBAAqB;AAGxE,qCAAmB,QAAQ,OAAO,IAAI;AACtC,gDAA8B,OAAO;AAErC,wBAAM,sBAAsB,UAAU,QAAQ,OAAO;AAErD,gDAA8B,IAAI;AAAA,gBACpC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,gCAAgC,SAAS;AAChD;AACE,gBAAI,SAAS;AACX,kBAAI,QAAQ,QAAQ;AACpB,kBAAI,QAAQ,qCAAqC,QAAQ,MAAM,QAAQ,SAAS,QAAQ,MAAM,OAAO,IAAI;AACzG,iCAAmB,KAAK;AAAA,YAC1B,OAAO;AACL,iCAAmB,IAAI;AAAA,YACzB;AAAA,UACF;AAAA,QACF;AAEA,YAAI;AAEJ;AACE,0CAAgC;AAAA,QAClC;AAEA,iBAAS,8BAA8B;AACrC,cAAI,kBAAkB,SAAS;AAC7B,gBAAI,OAAO,yBAAyB,kBAAkB,QAAQ,IAAI;AAElE,gBAAI,MAAM;AACR,qBAAO,qCAAqC,OAAO;AAAA,YACrD;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,iBAAS,2BAA2B,QAAQ;AAC1C,cAAI,WAAW,QAAW;AACxB,gBAAI,WAAW,OAAO,SAAS,QAAQ,aAAa,EAAE;AACtD,gBAAI,aAAa,OAAO;AACxB,mBAAO,4BAA4B,WAAW,MAAM,aAAa;AAAA,UACnE;AAEA,iBAAO;AAAA,QACT;AAEA,iBAAS,mCAAmC,cAAc;AACxD,cAAI,iBAAiB,QAAQ,iBAAiB,QAAW;AACvD,mBAAO,2BAA2B,aAAa,QAAQ;AAAA,UACzD;AAEA,iBAAO;AAAA,QACT;AAQA,YAAI,wBAAwB,CAAC;AAE7B,iBAAS,6BAA6B,YAAY;AAChD,cAAI,OAAO,4BAA4B;AAEvC,cAAI,CAAC,MAAM;AACT,gBAAI,aAAa,OAAO,eAAe,WAAW,aAAa,WAAW,eAAe,WAAW;AAEpG,gBAAI,YAAY;AACd,qBAAO,gDAAgD,aAAa;AAAA,YACtE;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAcA,iBAAS,oBAAoB,SAAS,YAAY;AAChD,cAAI,CAAC,QAAQ,UAAU,QAAQ,OAAO,aAAa,QAAQ,OAAO,MAAM;AACtE;AAAA,UACF;AAEA,kBAAQ,OAAO,YAAY;AAC3B,cAAI,4BAA4B,6BAA6B,UAAU;AAEvE,cAAI,sBAAsB,yBAAyB,GAAG;AACpD;AAAA,UACF;AAEA,gCAAsB,yBAAyB,IAAI;AAInD,cAAI,aAAa;AAEjB,cAAI,WAAW,QAAQ,UAAU,QAAQ,WAAW,kBAAkB,SAAS;AAE7E,yBAAa,iCAAiC,yBAAyB,QAAQ,OAAO,IAAI,IAAI;AAAA,UAChG;AAEA;AACE,4CAAgC,OAAO;AAEvC,kBAAM,6HAAkI,2BAA2B,UAAU;AAE7K,4CAAgC,IAAI;AAAA,UACtC;AAAA,QACF;AAYA,iBAAS,kBAAkB,MAAM,YAAY;AAC3C,cAAI,OAAO,SAAS,UAAU;AAC5B;AAAA,UACF;AAEA,cAAI,QAAQ,IAAI,GAAG;AACjB,qBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,kBAAI,QAAQ,KAAK,CAAC;AAElB,kBAAI,eAAe,KAAK,GAAG;AACzB,oCAAoB,OAAO,UAAU;AAAA,cACvC;AAAA,YACF;AAAA,UACF,WAAW,eAAe,IAAI,GAAG;AAE/B,gBAAI,KAAK,QAAQ;AACf,mBAAK,OAAO,YAAY;AAAA,YAC1B;AAAA,UACF,WAAW,MAAM;AACf,gBAAI,aAAa,cAAc,IAAI;AAEnC,gBAAI,OAAO,eAAe,YAAY;AAGpC,kBAAI,eAAe,KAAK,SAAS;AAC/B,oBAAI,WAAW,WAAW,KAAK,IAAI;AACnC,oBAAI;AAEJ,uBAAO,EAAE,OAAO,SAAS,KAAK,GAAG,MAAM;AACrC,sBAAI,eAAe,KAAK,KAAK,GAAG;AAC9B,wCAAoB,KAAK,OAAO,UAAU;AAAA,kBAC5C;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AASA,iBAAS,kBAAkB,SAAS;AAClC;AACE,gBAAI,OAAO,QAAQ;AAEnB,gBAAI,SAAS,QAAQ,SAAS,UAAa,OAAO,SAAS,UAAU;AACnE;AAAA,YACF;AAEA,gBAAI;AAEJ,gBAAI,OAAO,SAAS,YAAY;AAC9B,0BAAY,KAAK;AAAA,YACnB,WAAW,OAAO,SAAS,aAAa,KAAK,aAAa;AAAA;AAAA,YAE1D,KAAK,aAAa,kBAAkB;AAClC,0BAAY,KAAK;AAAA,YACnB,OAAO;AACL;AAAA,YACF;AAEA,gBAAI,WAAW;AAEb,kBAAI,OAAO,yBAAyB,IAAI;AACxC,6BAAe,WAAW,QAAQ,OAAO,QAAQ,MAAM,OAAO;AAAA,YAChE,WAAW,KAAK,cAAc,UAAa,CAAC,+BAA+B;AACzE,8CAAgC;AAEhC,kBAAI,QAAQ,yBAAyB,IAAI;AAEzC,oBAAM,uGAAuG,SAAS,SAAS;AAAA,YACjI;AAEA,gBAAI,OAAO,KAAK,oBAAoB,cAAc,CAAC,KAAK,gBAAgB,sBAAsB;AAC5F,oBAAM,4HAAiI;AAAA,YACzI;AAAA,UACF;AAAA,QACF;AAOA,iBAAS,sBAAsB,UAAU;AACvC;AACE,gBAAI,OAAO,OAAO,KAAK,SAAS,KAAK;AAErC,qBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,kBAAI,MAAM,KAAK,CAAC;AAEhB,kBAAI,QAAQ,cAAc,QAAQ,OAAO;AACvC,gDAAgC,QAAQ;AAExC,sBAAM,4GAAiH,GAAG;AAE1H,gDAAgC,IAAI;AACpC;AAAA,cACF;AAAA,YACF;AAEA,gBAAI,SAAS,QAAQ,MAAM;AACzB,8CAAgC,QAAQ;AAExC,oBAAM,uDAAuD;AAE7D,8CAAgC,IAAI;AAAA,YACtC;AAAA,UACF;AAAA,QACF;AACA,iBAAS,4BAA4B,MAAM,OAAO,UAAU;AAC1D,cAAI,YAAY,mBAAmB,IAAI;AAGvC,cAAI,CAAC,WAAW;AACd,gBAAI,OAAO;AAEX,gBAAI,SAAS,UAAa,OAAO,SAAS,YAAY,SAAS,QAAQ,OAAO,KAAK,IAAI,EAAE,WAAW,GAAG;AACrG,sBAAQ;AAAA,YACV;AAEA,gBAAI,aAAa,mCAAmC,KAAK;AAEzD,gBAAI,YAAY;AACd,sBAAQ;AAAA,YACV,OAAO;AACL,sBAAQ,4BAA4B;AAAA,YACtC;AAEA,gBAAI;AAEJ,gBAAI,SAAS,MAAM;AACjB,2BAAa;AAAA,YACf,WAAW,QAAQ,IAAI,GAAG;AACxB,2BAAa;AAAA,YACf,WAAW,SAAS,UAAa,KAAK,aAAa,oBAAoB;AACrE,2BAAa,OAAO,yBAAyB,KAAK,IAAI,KAAK,aAAa;AACxE,qBAAO;AAAA,YACT,OAAO;AACL,2BAAa,OAAO;AAAA,YACtB;AAEA;AACE,oBAAM,qJAA+J,YAAY,IAAI;AAAA,YACvL;AAAA,UACF;AAEA,cAAI,UAAU,cAAc,MAAM,MAAM,SAAS;AAGjD,cAAI,WAAW,MAAM;AACnB,mBAAO;AAAA,UACT;AAOA,cAAI,WAAW;AACb,qBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,gCAAkB,UAAU,CAAC,GAAG,IAAI;AAAA,YACtC;AAAA,UACF;AAEA,cAAI,SAAS,qBAAqB;AAChC,kCAAsB,OAAO;AAAA,UAC/B,OAAO;AACL,8BAAkB,OAAO;AAAA,UAC3B;AAEA,iBAAO;AAAA,QACT;AACA,YAAI,sCAAsC;AAC1C,iBAAS,4BAA4B,MAAM;AACzC,cAAI,mBAAmB,4BAA4B,KAAK,MAAM,IAAI;AAClE,2BAAiB,OAAO;AAExB;AACE,gBAAI,CAAC,qCAAqC;AACxC,oDAAsC;AAEtC,mBAAK,sJAAgK;AAAA,YACvK;AAGA,mBAAO,eAAe,kBAAkB,QAAQ;AAAA,cAC9C,YAAY;AAAA,cACZ,KAAK,WAAY;AACf,qBAAK,2FAAgG;AAErG,uBAAO,eAAe,MAAM,QAAQ;AAAA,kBAClC,OAAO;AAAA,gBACT,CAAC;AACD,uBAAO;AAAA,cACT;AAAA,YACF,CAAC;AAAA,UACH;AAEA,iBAAO;AAAA,QACT;AACA,iBAAS,2BAA2B,SAAS,OAAO,UAAU;AAC5D,cAAI,aAAa,aAAa,MAAM,MAAM,SAAS;AAEnD,mBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,8BAAkB,UAAU,CAAC,GAAG,WAAW,IAAI;AAAA,UACjD;AAEA,4BAAkB,UAAU;AAC5B,iBAAO;AAAA,QACT;AAEA,iBAAS,gBAAgB,OAAO,SAAS;AACvC,cAAI,iBAAiB,wBAAwB;AAC7C,kCAAwB,aAAa,CAAC;AACtC,cAAI,oBAAoB,wBAAwB;AAEhD;AACE,oCAAwB,WAAW,iBAAiB,oBAAI,IAAI;AAAA,UAC9D;AAEA,cAAI;AACF,kBAAM;AAAA,UACR,UAAE;AACA,oCAAwB,aAAa;AAErC;AACE,kBAAI,mBAAmB,QAAQ,kBAAkB,gBAAgB;AAC/D,oBAAI,qBAAqB,kBAAkB,eAAe;AAE1D,oBAAI,qBAAqB,IAAI;AAC3B,uBAAK,qMAA+M;AAAA,gBACtN;AAEA,kCAAkB,eAAe,MAAM;AAAA,cACzC;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,YAAI,6BAA6B;AACjC,YAAI,kBAAkB;AACtB,iBAAS,YAAY,MAAM;AACzB,cAAI,oBAAoB,MAAM;AAC5B,gBAAI;AAGF,kBAAI,iBAAiB,YAAY,KAAK,OAAO,GAAG,MAAM,GAAG,CAAC;AAC1D,kBAAI,cAAc,UAAU,OAAO,aAAa;AAGhD,gCAAkB,YAAY,KAAK,QAAQ,QAAQ,EAAE;AAAA,YACvD,SAAS,MAAM;AAIb,gCAAkB,SAAU,UAAU;AACpC;AACE,sBAAI,+BAA+B,OAAO;AACxC,iDAA6B;AAE7B,wBAAI,OAAO,mBAAmB,aAAa;AACzC,4BAAM,0NAAyO;AAAA,oBACjP;AAAA,kBACF;AAAA,gBACF;AAEA,oBAAI,UAAU,IAAI,eAAe;AACjC,wBAAQ,MAAM,YAAY;AAC1B,wBAAQ,MAAM,YAAY,MAAS;AAAA,cACrC;AAAA,YACF;AAAA,UACF;AAEA,iBAAO,gBAAgB,IAAI;AAAA,QAC7B;AAEA,YAAI,gBAAgB;AACpB,YAAI,oBAAoB;AACxB,iBAAS,IAAI,UAAU;AACrB;AAGE,gBAAI,oBAAoB;AACxB;AAEA,gBAAI,qBAAqB,YAAY,MAAM;AAGzC,mCAAqB,UAAU,CAAC;AAAA,YAClC;AAEA,gBAAI,uBAAuB,qBAAqB;AAChD,gBAAI;AAEJ,gBAAI;AAKF,mCAAqB,mBAAmB;AACxC,uBAAS,SAAS;AAIlB,kBAAI,CAAC,wBAAwB,qBAAqB,yBAAyB;AACzE,oBAAI,QAAQ,qBAAqB;AAEjC,oBAAI,UAAU,MAAM;AAClB,uCAAqB,0BAA0B;AAC/C,gCAAc,KAAK;AAAA,gBACrB;AAAA,cACF;AAAA,YACF,SAASD,QAAO;AACd,0BAAY,iBAAiB;AAC7B,oBAAMA;AAAA,YACR,UAAE;AACA,mCAAqB,mBAAmB;AAAA,YAC1C;AAEA,gBAAI,WAAW,QAAQ,OAAO,WAAW,YAAY,OAAO,OAAO,SAAS,YAAY;AACtF,kBAAI,iBAAiB;AAGrB,kBAAI,aAAa;AACjB,kBAAI,WAAW;AAAA,gBACb,MAAM,SAAU,SAAS,QAAQ;AAC/B,+BAAa;AACb,iCAAe,KAAK,SAAUE,cAAa;AACzC,gCAAY,iBAAiB;AAE7B,wBAAI,kBAAkB,GAAG;AAGvB,mDAA6BA,cAAa,SAAS,MAAM;AAAA,oBAC3D,OAAO;AACL,8BAAQA,YAAW;AAAA,oBACrB;AAAA,kBACF,GAAG,SAAUF,QAAO;AAElB,gCAAY,iBAAiB;AAC7B,2BAAOA,MAAK;AAAA,kBACd,CAAC;AAAA,gBACH;AAAA,cACF;AAEA;AACE,oBAAI,CAAC,qBAAqB,OAAO,YAAY,aAAa;AAExD,0BAAQ,QAAQ,EAAE,KAAK,WAAY;AAAA,kBAAC,CAAC,EAAE,KAAK,WAAY;AACtD,wBAAI,CAAC,YAAY;AACf,0CAAoB;AAEpB,4BAAM,mMAAuN;AAAA,oBAC/N;AAAA,kBACF,CAAC;AAAA,gBACH;AAAA,cACF;AAEA,qBAAO;AAAA,YACT,OAAO;AACL,kBAAI,cAAc;AAGlB,0BAAY,iBAAiB;AAE7B,kBAAI,kBAAkB,GAAG;AAEvB,oBAAI,SAAS,qBAAqB;AAElC,oBAAI,WAAW,MAAM;AACnB,gCAAc,MAAM;AACpB,uCAAqB,UAAU;AAAA,gBACjC;AAIA,oBAAI,YAAY;AAAA,kBACd,MAAM,SAAU,SAAS,QAAQ;AAI/B,wBAAI,qBAAqB,YAAY,MAAM;AAEzC,2CAAqB,UAAU,CAAC;AAChC,mDAA6B,aAAa,SAAS,MAAM;AAAA,oBAC3D,OAAO;AACL,8BAAQ,WAAW;AAAA,oBACrB;AAAA,kBACF;AAAA,gBACF;AACA,uBAAO;AAAA,cACT,OAAO;AAGL,oBAAI,aAAa;AAAA,kBACf,MAAM,SAAU,SAAS,QAAQ;AAC/B,4BAAQ,WAAW;AAAA,kBACrB;AAAA,gBACF;AACA,uBAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,YAAY,mBAAmB;AACtC;AACE,gBAAI,sBAAsB,gBAAgB,GAAG;AAC3C,oBAAM,kIAAuI;AAAA,YAC/I;AAEA,4BAAgB;AAAA,UAClB;AAAA,QACF;AAEA,iBAAS,6BAA6B,aAAa,SAAS,QAAQ;AAClE;AACE,gBAAI,QAAQ,qBAAqB;AAEjC,gBAAI,UAAU,MAAM;AAClB,kBAAI;AACF,8BAAc,KAAK;AACnB,4BAAY,WAAY;AACtB,sBAAI,MAAM,WAAW,GAAG;AAEtB,yCAAqB,UAAU;AAC/B,4BAAQ,WAAW;AAAA,kBACrB,OAAO;AAEL,iDAA6B,aAAa,SAAS,MAAM;AAAA,kBAC3D;AAAA,gBACF,CAAC;AAAA,cACH,SAASA,QAAO;AACd,uBAAOA,MAAK;AAAA,cACd;AAAA,YACF,OAAO;AACL,sBAAQ,WAAW;AAAA,YACrB;AAAA,UACF;AAAA,QACF;AAEA,YAAI,aAAa;AAEjB,iBAAS,cAAc,OAAO;AAC5B;AACE,gBAAI,CAAC,YAAY;AAEf,2BAAa;AACb,kBAAI,IAAI;AAER,kBAAI;AACF,uBAAO,IAAI,MAAM,QAAQ,KAAK;AAC5B,sBAAI,WAAW,MAAM,CAAC;AAEtB,qBAAG;AACD,+BAAW,SAAS,IAAI;AAAA,kBAC1B,SAAS,aAAa;AAAA,gBACxB;AAEA,sBAAM,SAAS;AAAA,cACjB,SAASA,QAAO;AAEd,wBAAQ,MAAM,MAAM,IAAI,CAAC;AACzB,sBAAMA;AAAA,cACR,UAAE;AACA,6BAAa;AAAA,cACf;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,YAAI,kBAAmB;AACvB,YAAI,iBAAkB;AACtB,YAAI,gBAAiB;AACrB,YAAI,WAAW;AAAA,UACb,KAAK;AAAA,UACL,SAAS;AAAA,UACT,OAAO;AAAA,UACP;AAAA,UACA,MAAM;AAAA,QACR;AAEA,gBAAQ,WAAW;AACnB,gBAAQ,YAAY;AACpB,gBAAQ,WAAW;AACnB,gBAAQ,WAAW;AACnB,gBAAQ,gBAAgB;AACxB,gBAAQ,aAAa;AACrB,gBAAQ,WAAW;AACnB,gBAAQ,qDAAqD;AAC7D,gBAAQ,MAAM;AACd,gBAAQ,eAAe;AACvB,gBAAQ,gBAAgB;AACxB,gBAAQ,gBAAgB;AACxB,gBAAQ,gBAAgB;AACxB,gBAAQ,YAAY;AACpB,gBAAQ,aAAa;AACrB,gBAAQ,iBAAiB;AACzB,gBAAQ,OAAO;AACf,gBAAQ,OAAO;AACf,gBAAQ,kBAAkB;AAC1B,gBAAQ,eAAe;AACvB,gBAAQ,cAAc;AACtB,gBAAQ,aAAa;AACrB,gBAAQ,gBAAgB;AACxB,gBAAQ,mBAAmB;AAC3B,gBAAQ,YAAY;AACpB,gBAAQ,QAAQ;AAChB,gBAAQ,sBAAsB;AAC9B,gBAAQ,qBAAqB;AAC7B,gBAAQ,kBAAkB;AAC1B,gBAAQ,UAAU;AAClB,gBAAQ,aAAa;AACrB,gBAAQ,SAAS;AACjB,gBAAQ,WAAW;AACnB,gBAAQ,uBAAuB;AAC/B,gBAAQ,gBAAgB;AACxB,gBAAQ,UAAU;AAElB,YACE,OAAO,mCAAmC,eAC1C,OAAO,+BAA+B,+BACpC,YACF;AACA,yCAA+B,2BAA2B,IAAI,MAAM,CAAC;AAAA,QACvE;AAAA,MAEE,GAAG;AAAA,IACL;AAAA;AAAA;;;ACnrFA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;", "names": ["ReactDebugCurrentFrame", "moduleObject", "error", "Component", "returnValue"]}