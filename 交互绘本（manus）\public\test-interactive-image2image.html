<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试交互环节Image2Image功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .result-image {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 10px 0;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 12px;
        }
        .reference-display {
            background: #e8f5e8;
            border: 1px solid #28a745;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ 测试交互环节Image2Image功能</h1>
        <p>验证在交互环节中，Image2Image功能是否正确使用新生成的第一页插画作为参考图片（sourceImage）。</p>
        
        <div class="reference-display">
            <h3>🎯 当前参考图片（第一页插画）</h3>
            <p><strong>URL:</strong> <span id="referenceUrl">加载中...</span></p>
            <div id="referenceImageContainer"></div>
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button id="testBtn" onclick="testInteractiveImage2Image()">🚀 测试交互Image2Image</button>
            <button onclick="clearResults()">🗑️ 清除结果</button>
        </div>
        
        <div id="status"></div>
        <div id="log" class="log" style="display: none;"></div>
        <div id="result"></div>
    </div>

    <script type="module">
        // 日志函数
        function log(message) {
            const logDiv = document.getElementById('log');
            logDiv.style.display = 'block';
            logDiv.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        // 显示状态
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
        }

        // 清除结果
        window.clearResults = function() {
            document.getElementById('result').innerHTML = '';
            document.getElementById('log').innerHTML = '';
            document.getElementById('log').style.display = 'none';
            showStatus('结果已清除', 'info');
        };

        // 初始化页面
        async function initializePage() {
            try {
                // 导入liblibService
                const { default: liblibService } = await import('/src/services/liblibService.js');
                
                // 初始化API密钥
                const accessKey = 'VXlp-nUZOKSUC0bMSUqA_w';
                const secretKey = 'vyJel3b6lcZPTmcybbZpQ2jdhvsWXQch';
                liblibService.initializeApiKeys(accessKey, secretKey);
                
                // 获取当前参考图片URL
                const referenceUrl = liblibService.getDefaultReferenceImageUrl();
                document.getElementById('referenceUrl').textContent = referenceUrl;
                
                // 显示参考图片
                const referenceContainer = document.getElementById('referenceImageContainer');
                referenceContainer.innerHTML = `
                    <img src="${referenceUrl}" alt="参考图片（第一页插画）" class="result-image" 
                         onload="console.log('参考图片加载成功')" 
                         onerror="console.error('参考图片加载失败')">
                `;
                
                console.log('页面初始化完成，参考图片URL:', referenceUrl);
                
            } catch (error) {
                console.error('页面初始化失败:', error);
                showStatus('页面初始化失败: ' + error.message, 'error');
            }
        }

        // 测试交互环节的Image2Image功能
        window.testInteractiveImage2Image = async function() {
            const btn = document.getElementById('testBtn');
            btn.disabled = true;
            btn.textContent = '⏳ 测试中...';
            
            try {
                log('🖼️ 开始测试交互环节Image2Image功能');
                showStatus('正在初始化测试...', 'info');
                
                // 导入liblibService
                const { default: liblibService } = await import('/src/services/liblibService.js');
                
                // 获取参考图片URL
                const referenceUrl = liblibService.getDefaultReferenceImageUrl();
                log('🎯 使用参考图片: ' + referenceUrl);
                log('📝 这是新生成的第一页插画，将作为所有交互环节的风格参考');
                
                showStatus('开始Image2Image生成...', 'warning');
                
                // 模拟交互环节的场景描述
                const interactivePrompt = "a cute brown bear and a white rabbit playing together in the forest, children's illustration style";
                
                log('🔤 交互场景提示词: ' + interactivePrompt);
                log('📏 提示词长度: ' + interactivePrompt.length + ' 字符');
                
                showStatus('正在进行Image2Image生成，预计需要1-2分钟...', 'warning');
                
                const startTime = Date.now();
                log('⏳ 开始调用Image2Image API');
                log('🎯 使用controlnet + IPAdapter模式进行风格迁移');
                
                // 使用Image2Image功能，基于第一页插画生成新场景
                const imageUrl = await liblibService.generateImageFromImage(
                    referenceUrl, 
                    interactivePrompt, 
                    '6-8岁'
                );
                
                const endTime = Date.now();
                const duration = Math.round((endTime - startTime) / 1000);
                
                log('🎉 交互Image2Image生成成功！');
                log('🔗 生成图片URL: ' + imageUrl);
                log('⏱️ 生成耗时: ' + duration + ' 秒');
                
                // 显示结果
                showStatus('交互Image2Image测试成功！', 'success');
                
                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = `
                    <h3>🎉 交互环节Image2Image测试成功</h3>
                    
                    <div class="comparison">
                        <div class="comparison-item">
                            <h4>🎯 参考图片（第一页插画）</h4>
                            <img src="${referenceUrl}" alt="参考图片" class="result-image">
                            <p><small>作为风格参考的源图片</small></p>
                        </div>
                        
                        <div class="comparison-item">
                            <h4>🖼️ 生成结果（交互场景）</h4>
                            <img src="${imageUrl}" alt="生成结果" class="result-image">
                            <p><small>基于参考图片风格生成的新场景</small></p>
                        </div>
                    </div>
                    
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 15px 0;">
                        <h4>📊 测试结果分析</h4>
                        <p><strong>✅ 功能验证:</strong> Image2Image功能正常工作</p>
                        <p><strong>✅ 参考图片:</strong> 正确使用新生成的第一页插画</p>
                        <p><strong>✅ 风格一致性:</strong> 生成的图片应保持与第一页相同的风格</p>
                        <p><strong>✅ 角色一致性:</strong> 小熊波波的形象应保持一致</p>
                        <p><strong>⏱️ 生成耗时:</strong> ${duration} 秒</p>
                        <p><strong>🎯 使用模式:</strong> controlnet + IPAdapter风格迁移</p>
                    </div>
                    
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 4px; margin: 15px 0; border: 1px solid #28a745;">
                        <h4>🎯 交互环节应用</h4>
                        <p><strong>参考图片URL:</strong> ${referenceUrl}</p>
                        <p><strong>生成图片URL:</strong> ${imageUrl}</p>
                        <p><strong>应用场景:</strong> 在交互绘本的选择环节，用户选择不同选项时，系统会基于第一页插画的风格生成对应的场景图片，确保整个故事的视觉风格保持一致。</p>
                    </div>
                `;
                
                // 保存测试结果
                const result = {
                    testType: 'interactive_image2image',
                    referenceImageUrl: referenceUrl,
                    generatedImageUrl: imageUrl,
                    prompt: interactivePrompt,
                    duration: duration,
                    generatedAt: new Date().toISOString(),
                    success: true
                };
                
                localStorage.setItem('interactive_image2image_test', JSON.stringify(result));
                log('💾 测试结果已保存到浏览器本地存储');
                log('🎉 交互环节Image2Image功能验证完成！');
                
            } catch (error) {
                log('❌ 交互Image2Image测试失败: ' + error.message);
                console.error('详细错误:', error);
                showStatus('测试失败: ' + error.message, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '🚀 测试交互Image2Image';
            }
        };

        // 页面加载时初始化
        window.addEventListener('load', initializePage);
    </script>
</body>
</html>
