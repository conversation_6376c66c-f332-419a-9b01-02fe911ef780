# API 配置示例文件
# 复制此文件为 .env 并填入真实的API密钥

# LIBLIB AI API 配置（用于图片生成）
VITE_LIBLIB_ACCESS_KEY=your_liblib_access_key_here
VITE_LIBLIB_SECRET_KEY=your_liblib_secret_key_here

# OpenAI API 配置（用于故事生成）
# 请在此处设置您的OpenAI API密钥
VITE_OPENAI_API_KEY=your_openai_api_key_here

# 应用配置
VITE_APP_TITLE=小熊波波的友谊冒险
VITE_APP_DESCRIPTION=专为6-8岁自闭症儿童设计的交互式绘本

# 开发环境配置
VITE_DEV_MODE=true

# 使用说明：
# 1. 将此文件复制为 .env
# 2. 替换 your_openai_api_key_here 为您的真实OpenAI API密钥
# 3. 如需使用图片生成功能，也请设置LIBLIB AI的密钥
# 4. 保存文件后重启开发服务器
