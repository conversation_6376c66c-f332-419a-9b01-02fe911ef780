# 小熊波波的友谊冒险 - 使用说明

## 应用简介

这是一个专为6-8岁自闭症儿童设计的交互式数字绘本，通过小熊波波学习友谊的温馨故事，帮助孩子发展社交技能和情感表达能力。

## 主要功能

### 1. 交互式绘本阅读
- **12页完整故事**：讲述小熊波波从害羞到主动交友的成长过程
- **语音朗读**：自动朗读每页内容，支持中文语音合成
- **进度显示**：清晰显示阅读进度和页面导航

### 2. 智能交互环节
- **3个交互问题**：在第4、8、11页设置开放性问题
- **多种输入方式**：支持文字输入和语音输入
- **智能引导**：30秒内未回答会提供引导提示
- **个性化插画**：根据回答内容生成专属插画

### 3. 能力评估系统
- **四维度评估**：语言词汇量、思维逻辑、社会适应、情感识别
- **详细分析报告**：提供具体的能力分析和改进建议
- **个性化建议**：针对每个维度给出专业指导

## 使用流程

### 第一步：启动应用
1. 打开应用后，会看到介绍页面
2. 阅读应用说明和功能介绍
3. 如需使用AI功能，点击"设置OpenAI API密钥"

### 第二步：开始阅读
1. 点击"开始阅读"按钮
2. 应用会自动朗读页面内容
3. 使用"上一页"/"下一页"按钮导航

### 第三步：参与交互
当遇到交互页面时：
1. 仔细听取问题内容
2. 在30秒内回答问题：
   - 点击"开始语音输入"进行语音回答
   - 或在文本框中输入文字回答
3. 点击"提交回答"确认
4. 查看基于回答生成的个性化插画

### 第四步：查看评估报告
完成所有交互后：
1. 系统自动生成评估报告
2. 查看四个维度的得分和分析
3. 阅读个性化改进建议
4. 可选择"重新开始"再次体验

## 操作指南

### 语音功能
- **朗读内容**：点击"朗读内容"按钮可重新播放页面内容
- **语音输入**：点击"开始语音输入"，对着麦克风说话
- **停止语音**：翻页时会自动停止当前语音播放

### 交互问题
- **回答要求**：鼓励完整表达，不是简单的是/否回答
- **时间限制**：30秒内回答，超时会显示引导提示
- **修改回答**：可以重新编辑和提交回答

### AI功能（需要API密钥）
- **故事生成**：可以生成新的故事主题和内容
- **插画生成**：根据回答内容创建个性化插画
- **智能评估**：使用AI分析回答质量和能力水平

## 教育价值

### 社交技能培养
- 学习如何主动打招呼
- 理解友谊的建立过程
- 掌握基本的社交礼仪

### 情感表达训练
- 识别和表达不同情绪
- 理解他人的感受
- 培养共情能力

### 语言能力提升
- 扩展词汇量
- 练习完整表达
- 提高语言组织能力

### 逻辑思维发展
- 理解因果关系
- 培养推理能力
- 增强问题解决技能

## 使用建议

### 对于家长
1. **陪伴使用**：建议家长陪同孩子一起使用
2. **鼓励表达**：鼓励孩子用自己的话回答问题
3. **讨论故事**：可以和孩子讨论故事内容和感受
4. **应用实践**：将故事中的社交技能应用到日常生活

### 对于教师
1. **课堂使用**：可作为社交技能训练的辅助工具
2. **小组活动**：组织学生讨论故事情节和角色
3. **评估参考**：使用评估报告了解学生能力水平
4. **个性化指导**：根据报告建议制定个性化教学计划

### 对于治疗师
1. **治疗辅助**：作为社交技能训练的补充工具
2. **能力评估**：参考评估结果制定干预计划
3. **进度跟踪**：定期使用以观察能力发展
4. **家庭作业**：推荐给家庭进行日常练习

## 技术要求

### 设备要求
- 电脑、平板或智能手机
- 支持现代浏览器（Chrome、Firefox、Safari等）
- 具备麦克风和扬声器功能

### 网络要求
- 稳定的互联网连接
- 如使用AI功能，需要访问OpenAI服务

### 浏览器设置
- 允许麦克风访问权限
- 启用JavaScript
- 允许自动播放音频

## 常见问题

### Q: 语音识别不工作怎么办？
A: 请检查浏览器麦克风权限，确保使用支持Web Speech API的浏览器。

### Q: 如何获取OpenAI API密钥？
A: 访问OpenAI官网注册账户，在API设置中创建新的API密钥。

### Q: 应用是否会保存个人信息？
A: 所有数据仅存储在本地浏览器中，不会上传到服务器。

### Q: 可以重复使用吗？
A: 可以，每次使用都会生成新的评估报告，有助于跟踪进步。

### Q: 适合什么年龄段？
A: 主要针对6-8岁自闭症儿童设计，但也适合其他有社交技能训练需求的儿童。

## 联系支持

如有技术问题或使用建议，请通过以下方式联系：
- 项目GitHub页面提交Issue
- 发送邮件至技术支持邮箱

---

希望这个应用能够帮助孩子们在友谊的世界中快乐成长！
