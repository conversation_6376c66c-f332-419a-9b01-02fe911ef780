import { useState, useEffect } from 'react';
import { StoryContainer } from './components/StoryContainer';
import TestPage from './TestPage';

function App() {
  const [currentPage, setCurrentPage] = useState('main');

  // 检查URL参数来决定显示哪个页面
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const page = urlParams.get('page');
    if (page === 'test') {
      setCurrentPage('test');
    }
  }, []);

  if (currentPage === 'test') {
    return <TestPage />;
  }

  return (
    <div className="App">
      <StoryContainer />
    </div>
  );
}

export default App;
