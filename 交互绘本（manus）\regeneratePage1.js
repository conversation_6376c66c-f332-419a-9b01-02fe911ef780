// 重新生成绘本第一页插画的专用脚本
// 使用LIBLIB AI的text2image功能

import liblibService from './src/services/liblibService.js';

// 第一页的故事内容
const PAGE_1_CONTENT = "波波是一只住在森林里的小棕熊。他有一双好奇的大眼睛和一颗善良的心。每天早晨，波波都会坐在自己的小木屋前，望着远处的大树和花朵，但他从来不敢走得太远。";

// 构建专门的第一页提示词
function buildPage1Prompt() {
  return "a cute brown bear named <PERSON><PERSON> with big curious eyes, round face, small black nose, warm brown fur, kind and gentle expression, sitting in front of a small wooden house in the forest, looking at distant trees and flowers, morning sunlight filtering through leaves, children's watercolor illustration style, warm and friendly colors, soft gentle tones, clear outlines, designed for children aged 6-8, cozy and peaceful atmosphere, warm browns and greens, beautiful wildflowers, tall forest trees, peaceful morning scene, slightly shy but curious mood, high quality children's book illustration";
}

// 重新生成第一页插画
async function regeneratePage1Illustration() {
  console.log('🎨 开始重新生成《小熊波波的友谊冒险》第一页插画');
  console.log('=' .repeat(60));
  
  try {
    // 检查API密钥状态
    console.log('🔍 检查API状态...');
    const apiStatus = liblibService.getApiStatus();
    console.log('API状态:', {
      已初始化: apiStatus.isInitialized,
      有AccessKey: apiStatus.hasAccessKey,
      有SecretKey: apiStatus.hasSecretKey,
      基础URL: apiStatus.baseUrl,
      模板UUID: apiStatus.templateUuid
    });
    
    if (!apiStatus.isInitialized) {
      console.log('\n⚠️  API密钥未初始化！');
      console.log('正在尝试从环境变量初始化...');
      
      // 从环境变量初始化API密钥
      const accessKey = 'VXlp-nUZOKSUC0bMSUqA_w';
      const secretKey = 'vyJel3b6lcZPTmcybbZpQ2jdhvsWXQch';
      
      liblibService.initializeApiKeys(accessKey, secretKey);
      console.log('✅ API密钥初始化成功');
    } else {
      console.log('✅ API密钥已初始化');
    }
    
    // 显示要生成的内容
    console.log('\n📖 第一页故事内容:');
    console.log(PAGE_1_CONTENT);
    
    const prompt = buildPage1Prompt();
    console.log('\n🔤 生成的英文提示词:');
    console.log(prompt);
    console.log(`📏 提示词长度: ${prompt.length} 字符`);
    
    // 开始生成
    console.log('\n⏳ 正在使用Text2Image生成第一页插画...');
    console.log('⏰ 预计需要1-2分钟，请耐心等待...');
    console.log('🎯 使用参数: aspectRatio="square", imgCount=1, steps=30');
    
    const startTime = Date.now();
    
    // 直接调用text2image功能
    const imageUrl = await liblibService.generateImage(prompt, '6-8岁');
    
    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);
    
    console.log('\n🎉 第一页插画重新生成成功！');
    console.log('=' .repeat(60));
    console.log('📋 生成结果:');
    console.log(`   🔗 图片URL: ${imageUrl}`);
    console.log(`   ⏱️  生成耗时: ${duration} 秒`);
    console.log(`   📅 生成时间: ${new Date().toLocaleString()}`);
    console.log(`   🎯 页面: 第1页`);
    console.log(`   🎨 功能: Text2Image`);
    console.log(`   📐 尺寸: 1024x1024 (square)`);
    
    // 生成更新代码建议
    console.log('\n📝 更新storyData.ts的代码:');
    console.log('=' .repeat(60));
    console.log('将第1页的image字段更新为:');
    console.log(`image: "${imageUrl}"`);
    
    console.log('\n或者完整的页面对象:');
    console.log(`{
  id: 1,
  content: "${PAGE_1_CONTENT}",
  isInteractive: false,
  image: "${imageUrl}"
}`);
    
    // 保存结果到文件
    const result = {
      pageId: 1,
      content: PAGE_1_CONTENT,
      prompt: prompt,
      imageUrl: imageUrl,
      generatedAt: new Date().toISOString(),
      duration: duration,
      service: 'LIBLIB AI Text2Image',
      parameters: {
        aspectRatio: 'square',
        imgCount: 1,
        steps: 30,
        templateUuid: apiStatus.templateUuid
      }
    };
    
    // 尝试保存结果
    try {
      const fs = await import('fs');
      const filename = `page1_regenerated_${Date.now()}.json`;
      fs.writeFileSync(filename, JSON.stringify(result, null, 2));
      console.log(`\n💾 结果已保存到: ${filename}`);
    } catch (saveError) {
      console.log('\n💾 无法保存结果文件，但生成成功');
    }
    
    console.log('\n✨ 第一页插画重新生成完成！');
    console.log('🔗 请复制上面的图片URL用于更新您的故事数据');
    console.log('🎨 新插画应该比之前的更符合绘本风格');
    
    return result;
    
  } catch (error) {
    console.error('\n❌ 第一页插画重新生成失败:', error.message);
    console.error('错误详情:', error);
    
    // 提供故障排除建议
    console.log('\n🔧 故障排除建议:');
    console.log('1. 检查网络连接');
    console.log('2. 验证API密钥是否正确');
    console.log('3. 确认LIBLIB AI服务状态');
    console.log('4. 检查账户余额是否充足');
    console.log('5. 重试运行此脚本');
    
    return null;
  }
}

// 主函数
async function main() {
  console.log('🚀 启动第一页插画重新生成器');
  console.log('📚 故事：《小熊波波的友谊冒险》');
  console.log('🎯 目标：使用LIBLIB AI Text2Image重新生成第一页插画\n');
  
  const result = await regeneratePage1Illustration();
  
  if (result) {
    console.log('\n🎉 任务完成！新的第一页插画已成功生成');
    console.log('💾 请保存上面显示的图片URL用于更新故事数据');
    console.log('🔄 如果对结果不满意，可以再次运行此脚本重新生成');
  } else {
    console.log('\n💔 任务失败，请检查错误信息并重试');
  }
}

// 运行生成
main().catch(error => {
  console.error('💥 脚本执行失败:', error);
  process.exit(1);
});
