// 安全的API密钥处理
// 注意：实际部署时，应使用环境变量或安全的密钥管理服务

// 创建一个安全的API密钥存储类
class ApiKeyManager {
  constructor() {
    this._apiKey = null;
    this._isInitialized = false;
  }

  // 初始化API密钥
  initialize(apiKey) {
    if (!apiKey || typeof apiKey !== 'string' || apiKey.trim() === '') {
      throw new Error('无效的API密钥');
    }
    
    this._apiKey = apiKey;
    this._isInitialized = true;
    console.log('API密钥已安全初始化');
  }

  // 获取API密钥用于API调用
  getApiKey() {
    if (!this._isInitialized) {
      throw new Error('API密钥未初始化');
    }
    return this._apiKey;
  }

  // 检查API密钥是否已初始化
  isInitialized() {
    return this._isInitialized;
  }

  // 清除API密钥（用于会话结束时）
  clear() {
    this._apiKey = null;
    this._isInitialized = false;
    console.log('API密钥已清除');
  }
}

// 创建单例实例
const apiKeyManager = new ApiKeyManager();

// 导出单例实例
export default apiKeyManager;
