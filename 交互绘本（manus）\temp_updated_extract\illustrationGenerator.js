// 基于用户回答生成插画的核心逻辑实现

import { getApiKey } from './apiKeyManager';

// 风格描述常量
const STYLE_DESCRIPTION = `温暖友好的儿童插画，使用柔和的色彩和简单清晰的形状，角色表情丰富且易于理解。
水彩画风格，轮廓线条清晰，色彩饱和度适中。使用温暖的棕色、绿色、蓝色和黄色为主。
柔和的光影，避免强烈对比，确保视觉舒适。轻微的水彩纹理，保持整体平滑感。
场景应该简洁不复杂，背景元素适量，主体突出。`;

// 角色描述常量
const CHARACTER_DESCRIPTION = `小熊波波：棕色毛发的小熊，圆脸，大眼睛，友好的表情。
小兔莉莉：灰白色的兔子，长耳朵，温柔的表情。
乌龟老师：绿色的乌龟，戴着眼镜，智慧的表情。
松鼠兄弟：红棕色的松鼠，蓬松的尾巴，活泼的表情。`;

/**
 * 从用户回答中提取关键内容
 * @param {string} answer - 用户的回答内容
 * @returns {Object} 提取的关键内容
 */
function extractKeyContent(answer) {
  // 提取角色
  const characters = ['波波', '小熊', '莉莉', '小兔', '乌龟', '松鼠']
    .filter(char => answer.includes(char));
  
  // 提取动作和场景
  const actions = [];
  const actionKeywords = ['打招呼', '分享', '帮助', '玩', '说话', '微笑', '拥抱', '交朋友'];
  actionKeywords.forEach(action => {
    if (answer.includes(action)) actions.push(action);
  });
  
  // 提取情感
  const emotions = [];
  const emotionKeywords = ['开心', '高兴', '害怕', '紧张', '兴奋', '好奇', '担心', '勇敢', '友好'];
  emotionKeywords.forEach(emotion => {
    if (answer.includes(emotion)) emotions.push(emotion);
  });
  
  // 提取场景元素
  const sceneElements = [];
  const sceneKeywords = ['森林', '树', '花', '草地', '河流', '木屋', '阳光', '雨', '野餐'];
  sceneKeywords.forEach(element => {
    if (answer.includes(element)) sceneElements.push(element);
  });
  
  return {
    characters: characters.length > 0 ? characters : ['波波', '小熊'],
    actions: actions.length > 0 ? actions : ['微笑'],
    emotions: emotions.length > 0 ? emotions : ['友好'],
    sceneElements: sceneElements.length > 0 ? sceneElements : ['森林']
  };
}

/**
 * 构建图像生成提示词
 * @param {string} answer - 用户的回答内容
 * @param {Object} context - 当前故事上下文
 * @returns {string} 完整的提示词
 */
function buildImagePrompt(answer, context) {
  const keyContent = extractKeyContent(answer);
  
  // 构建场景描述
  let sceneDescription = `${keyContent.characters.join('和')}在${keyContent.sceneElements.join('和')}中`;
  
  // 添加动作描述
  if (keyContent.actions.length > 0) {
    sceneDescription += `${keyContent.actions.join('和')}`;
  }
  
  // 添加情感描述
  if (keyContent.emotions.length > 0) {
    sceneDescription += `，表情${keyContent.emotions.join('和')}`;
  }
  
  // 结合用户原始回答
  const promptBase = `为自闭症儿童绘本创建一幅插图，描绘：${sceneDescription}。
具体情境：${answer}
${CHARACTER_DESCRIPTION}
${STYLE_DESCRIPTION}`;
  
  // 添加上下文相关信息
  if (context && context.currentPage) {
    promptBase += `\n当前故事情境：${context.currentPage.content}`;
  }
  
  return promptBase;
}

/**
 * 获取参考图像URL
 * @param {number} currentPageIndex - 当前页面索引
 * @param {Array} allImages - 所有可用的图像URL
 * @returns {Array} 参考图像URL数组
 */
function getReferenceImages(currentPageIndex, allImages) {
  const references = [];
  
  // 添加前一页的图像作为参考（如果存在）
  if (currentPageIndex > 0) {
    const prevImage = allImages.find(img => img.pageIndex === currentPageIndex - 1);
    if (prevImage) references.push(prevImage.url);
  }
  
  // 添加后一页的图像作为参考（如果存在）
  if (currentPageIndex < allImages.length - 1) {
    const nextImage = allImages.find(img => img.pageIndex === currentPageIndex + 1);
    if (nextImage) references.push(nextImage.url);
  }
  
  // 如果没有找到相邻页面的图像，使用任何可用的图像
  if (references.length === 0 && allImages.length > 0) {
    references.push(allImages[0].url);
  }
  
  return references;
}

/**
 * 调用OpenAI API生成图像
 * @param {string} prompt - 图像生成提示词
 * @param {Array} referenceImages - 参考图像URL数组
 * @returns {Promise<string>} 生成的图像URL
 */
async function generateImage(prompt, referenceImages = []) {
  const apiKey = getApiKey();
  if (!apiKey) {
    throw new Error('API密钥未设置');
  }
  
  try {
    const requestBody = {
      model: "dall-e-3",
      prompt: prompt,
      n: 1,
      size: "1024x1024",
      quality: "standard",
      style: "natural"
    };
    
    // 如果有参考图像，添加到请求中
    if (referenceImages && referenceImages.length > 0) {
      requestBody.reference_images = referenceImages;
    }
    
    const response = await fetch('https://api.openai.com/v1/images/generations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`API错误: ${errorData.error?.message || '未知错误'}`);
    }
    
    const data = await response.json();
    return data.data[0].url;
  } catch (error) {
    console.error('图像生成失败:', error);
    throw error;
  }
}

/**
 * 保存生成的图像到本地缓存
 * @param {string} imageUrl - 生成的图像URL
 * @param {number} pageId - 页面ID
 * @returns {Promise<string>} 本地图像路径
 */
async function saveGeneratedImage(imageUrl, pageId) {
  try {
    // 在实际应用中，这里会实现将远程图像保存到本地的逻辑
    // 在前端应用中，可以使用localStorage或IndexedDB存储图像URL
    
    // 模拟保存过程
    console.log(`保存图像: ${imageUrl} 到页面ID: ${pageId}`);
    
    // 返回图像URL作为本地路径（在实际应用中会返回本地文件路径）
    return imageUrl;
  } catch (error) {
    console.error('保存图像失败:', error);
    throw error;
  }
}

/**
 * 主函数：根据用户回答生成插画
 * @param {string} answer - 用户的回答内容
 * @param {number} pageId - 交互页面ID
 * @param {Object} context - 当前故事上下文
 * @param {Array} allImages - 所有可用的图像
 * @returns {Promise<string>} 生成的图像URL或路径
 */
export async function generateIllustrationFromAnswer(answer, pageId, context, allImages) {
  try {
    // 构建提示词
    const prompt = buildImagePrompt(answer, context);
    
    // 获取参考图像
    const referenceImages = getReferenceImages(context.currentPageIndex, allImages);
    
    // 生成图像
    const imageUrl = await generateImage(prompt, referenceImages);
    
    // 保存图像
    const savedImagePath = await saveGeneratedImage(imageUrl, pageId);
    
    return savedImagePath;
  } catch (error) {
    console.error('根据回答生成插画失败:', error);
    throw error;
  }
}

/**
 * 检查生成的插画是否与现有风格一致
 * @param {string} generatedImageUrl - 生成的图像URL
 * @param {Array} referenceImages - 参考图像URL数组
 * @returns {Promise<boolean>} 是否风格一致
 */
export async function checkStyleConsistency(generatedImageUrl, referenceImages) {
  // 在实际应用中，这里可以实现风格一致性检查逻辑
  // 可以使用计算机视觉API或简单的颜色分析
  
  // 模拟检查过程
  console.log('检查风格一致性:', generatedImageUrl, referenceImages);
  
  // 默认返回一致
  return true;
}

export default {
  generateIllustrationFromAnswer,
  checkStyleConsistency,
  buildImagePrompt,
  extractKeyContent
};
