// 测试OpenAI API连接和故事生成功能
// 用于验证.env配置是否正确

import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

async function testOpenAIConnection() {
  console.log('🧪 开始测试OpenAI API连接...\n');

  // 1. 检查环境变量配置
  console.log('1. 检查环境变量配置:');
  const apiKey = process.env.VITE_OPENAI_API_KEY;
  
  if (!apiKey) {
    console.log('   ❌ VITE_OPENAI_API_KEY 未在.env文件中找到');
    console.log('   请确保.env文件中包含: VITE_OPENAI_API_KEY=your_api_key');
    return;
  }
  
  if (apiKey === 'your_openai_api_key_here') {
    console.log('   ❌ API密钥仍为默认值，请设置真实的OpenAI API密钥');
    return;
  }
  
  console.log('   ✅ API密钥已配置');
  console.log('   🔑 密钥前缀:', apiKey.substring(0, 10) + '...');
  console.log('');

  // 2. 测试API连接
  console.log('2. 测试OpenAI API连接:');
  
  try {
    const response = await fetch('https://api.openai.com/v1/models', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const error = await response.json();
      console.log('   ❌ API连接失败:', error.error?.message || '未知错误');
      console.log('   状态码:', response.status);
      return;
    }

    const data = await response.json();
    console.log('   ✅ API连接成功');
    console.log('   📊 可用模型数量:', data.data?.length || 0);
    
    // 检查是否有GPT-4o模型
    const hasGPT4o = data.data?.some(model => model.id.includes('gpt-4o'));
    console.log('   🤖 GPT-4o可用:', hasGPT4o ? '是' : '否');
    console.log('');

  } catch (error) {
    console.log('   ❌ 网络请求失败:', error.message);
    return;
  }

  // 3. 测试故事生成
  console.log('3. 测试故事生成功能:');
  
  const testPrompt = `
请为6-8岁的自闭症儿童创作一个关于"人际关系"的12页绘本故事。

要求：
1. 故事应该简单、清晰，使用具体而非抽象的语言
2. 每页内容控制在100-150字左右
3. 使用简单的句子结构和明确的因果关系
4. 在第4页、第8页和第11页设计交互环节
5. 交互问题应鼓励自闭症儿童进行语言表达

输出格式：
{
  "title": "故事标题",
  "ageGroup": "6-8岁",
  "theme": "人际关系",
  "pages": [
    {
      "id": 1,
      "content": "第1页内容...",
      "isInteractive": false,
      "imagePath": "/assets/images/page1.png"
    }
  ]
}

请只生成前3页作为测试。
`;

  try {
    console.log('   📝 发送测试请求...');
    
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: 'gpt-4o',
        messages: [
          { 
            role: 'system', 
            content: '你是一位专业的儿童绘本作家，擅长为自闭症儿童创作教育性故事。请严格按照JSON格式输出。' 
          },
          { role: 'user', content: testPrompt }
        ],
        temperature: 0.7,
        max_tokens: 1000
      })
    });

    if (!response.ok) {
      const error = await response.json();
      console.log('   ❌ 故事生成失败:', error.error?.message || '未知错误');
      console.log('   状态码:', response.status);
      return;
    }

    const data = await response.json();
    const storyContent = data.choices[0].message.content;
    
    console.log('   ✅ 故事生成成功');
    console.log('   📖 生成内容长度:', storyContent.length, '字符');
    
    // 尝试解析JSON
    try {
      const jsonMatch = storyContent.match(/```json\s*([\s\S]*?)\s*```/) || 
                       storyContent.match(/\{[\s\S]*\}/);
      
      if (jsonMatch) {
        const storyData = JSON.parse(jsonMatch[1] || jsonMatch[0]);
        console.log('   📚 故事标题:', storyData.title);
        console.log('   📄 页面数量:', storyData.pages?.length || 0);
        console.log('   🎯 主题:', storyData.theme);
        console.log('   👶 年龄段:', storyData.ageGroup);
        
        if (storyData.pages && storyData.pages.length > 0) {
          console.log('   📝 第一页内容预览:', storyData.pages[0].content.substring(0, 50) + '...');
        }
      } else {
        console.log('   ⚠️ 无法解析JSON格式，但内容已生成');
        console.log('   📄 内容预览:', storyContent.substring(0, 100) + '...');
      }
    } catch (parseError) {
      console.log('   ⚠️ JSON解析失败，但内容已生成');
      console.log('   📄 内容预览:', storyContent.substring(0, 100) + '...');
    }
    
    console.log('');

  } catch (error) {
    console.log('   ❌ 故事生成请求失败:', error.message);
    return;
  }

  // 4. 测试总结
  console.log('4. 测试总结:');
  console.log('   ✅ 环境变量配置正确');
  console.log('   ✅ OpenAI API连接成功');
  console.log('   ✅ 故事生成功能正常');
  console.log('');
  console.log('🎉 所有测试通过！您可以在浏览器中使用故事生成功能了。');
  console.log('');
  console.log('📝 使用步骤:');
  console.log('1. 打开浏览器访问 http://localhost:5173/');
  console.log('2. 点击 "🎨 生成新故事" 按钮');
  console.log('3. 选择一个主题');
  console.log('4. 点击 "🎨 生成故事" 等待完成');
  console.log('5. 开始阅读新生成的故事');
}

// 运行测试
testOpenAIConnection().catch(console.error);
