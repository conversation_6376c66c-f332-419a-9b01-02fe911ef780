# 《小熊波波的友谊冒险》插画生成指南

本指南将帮助您使用LIBLIB AI为绘本生成一套完整的插画。

## 🎯 生成目标

为以下9个非交互页面生成专业的儿童插画：
- 第1页：波波在小木屋前
- 第2页：波波离开家寻找歌声
- 第3页：波波发现莉莉在采花
- 第5页：波波和莉莉初次相遇
- 第6页：波波和莉莉一起采花
- 第7页：波波对野餐会既兴奋又紧张
- 第9页：森林野餐会场景
- 第10页：莉莉安慰波波
- 第12页：波波快乐地去拜访朋友

## 🔧 准备工作

### 1. 确认API密钥配置

确保您的`.env`文件中已正确设置LIBLIB API密钥：

```env
VITE_LIBLIB_ACCESS_KEY=your_access_key_here
VITE_LIBLIB_SECRET_KEY=your_secret_key_here
```

### 2. 安装依赖

如果还没有安装dotenv依赖，请运行：

```bash
npm install dotenv
```

## 🚀 生成插画

### 方法一：使用npm脚本（推荐）

```bash
npm run generate-illustrations
```

### 方法二：直接运行脚本

```bash
node generateIllustrations.js
```

## 📊 生成过程

### 生成流程
1. **初始化**：检查API密钥并初始化LIBLIB服务
2. **逐页生成**：按页面顺序依次生成插画
3. **延迟控制**：每张图片生成间隔3秒，避免API限制
4. **结果统计**：显示成功/失败统计和详细信息

### 预计时间
- 单张插画：1-2分钟
- 全套9张插画：约15-20分钟

### 控制台输出示例
```
🚀 开始生成《小熊波波的友谊冒险》插画
📊 总共需要生成 9 张插画
✅ LIBLIB API服务初始化成功

🎨 正在生成第1页插画 (1/9)
📝 页面内容: 波波是一只住在森林里的小棕熊。他有一双好奇的大眼睛和一颗善良的心...
✅ 第1页插画生成成功
🔗 图片URL: https://example.com/generated-image-1.jpg
⏳ 等待3秒后继续下一张...

...

📊 生成结果统计:
✅ 成功: 9 张
❌ 失败: 0 张
⏱️ 总耗时: 1200 秒
```

## 🎨 插画风格特点

### 设计理念
专为6-8岁自闭症儿童设计的温暖友好插画：

- **色彩**：柔和温暖的色调，主要使用棕色、绿色、蓝色、黄色
- **风格**：水彩画技法，线条清晰一致
- **背景**：简洁不复杂，避免过多干扰元素
- **表情**：清晰易识别的情感表达
- **一致性**：所有插画保持统一的艺术风格

### 角色设计
- **小熊波波**：棕色毛发，圆脸，大眼睛，善良表情
- **小兔莉莉**：雪白毛发，粉红鼻子，长耳朵，温柔友善
- **其他角色**：猫头鹰、松鼠兄弟、乌龟等森林朋友

## 📋 生成结果处理

### 1. 保存图片URL
生成完成后，控制台会显示所有成功生成的图片URL：

```
🎉 成功生成的插画:
   第1页: https://example.com/image1.jpg
   第2页: https://example.com/image2.jpg
   ...

📋 插画URL列表（用于更新故事数据）:
page1: "https://example.com/image1.jpg",
page2: "https://example.com/image2.jpg",
...
```

### 2. 更新故事数据
将生成的图片URL更新到`src/data/storyData.ts`文件中：

```typescript
{
  id: 1,
  content: "波波是一只住在森林里的小棕熊...",
  isInteractive: false,
  image: "https://example.com/generated-image1.jpg"  // 更新这里
},
```

### 3. 测试显示效果
更新完成后，启动应用测试插画显示：

```bash
npm run dev
```

## ⚠️ 注意事项

### API限制
- **并发限制**：默认5个并发生图任务
- **QPS限制**：发起生图任务1秒1次
- **查询无限制**：查询结果接口无QPS限制

### 错误处理
如果某些页面生成失败：

1. **检查API密钥**：确认密钥正确且有效
2. **检查网络**：确保网络连接稳定
3. **重新运行**：可以重新运行脚本，只生成失败的页面
4. **查看日志**：检查控制台错误信息

### 成本控制
- 每张图片生成会消耗API配额
- 建议在测试时先生成1-2张确认效果
- 确认满意后再批量生成全套插画

## 🔄 自定义和调整

### 修改提示词
如果需要调整插画风格，可以编辑`generateIllustrations.js`中的提示词：

```javascript
const STORY_PAGES = [
  {
    id: 1,
    content: "...",
    prompt: "您的自定义提示词..."  // 修改这里
  },
  // ...
];
```

### 调整生成参数
可以修改以下参数：
- 生成间隔时间（默认3秒）
- 图片尺寸和质量
- 风格描述和角色特征

## 📞 技术支持

如果遇到问题：

1. **检查环境配置**：确认.env文件和API密钥
2. **查看LIBLIB文档**：参考官方API文档
3. **检查网络连接**：确保能正常访问LIBLIB API
4. **查看错误日志**：分析具体错误信息

## 🎊 完成后的效果

生成完成后，您将获得：

- ✅ 9张专业的儿童插画
- ✅ 风格一致的视觉体验
- ✅ 专为自闭症儿童优化的设计
- ✅ 完整的《小熊波波的友谊冒险》绘本插画集

这些插画将为您的交互式绘本提供温暖、友好、专业的视觉支持，帮助自闭症儿童更好地理解和享受故事内容。
