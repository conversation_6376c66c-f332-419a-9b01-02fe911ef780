# 基于用户回答生成插画的提示词设计和风格一致性策略

## 提示词模板设计

### 基础模板
```
为自闭症儿童绘本创建一幅插图，描绘：{用户回答内容}。
风格：温暖友好的儿童插画，使用柔和的色彩和简单清晰的形状，角色表情丰富且易于理解。
场景应该简洁不复杂，背景元素适量，主体突出。
```

### 风格一致性描述符
```
艺术风格：水彩画风格，轮廓线条清晰，色彩饱和度适中
角色设计：保持与绘本中已有角色（小熊波波等）相同的视觉特征和比例
色彩方案：使用温暖的棕色、绿色、蓝色和黄色为主，与现有插画配色一致
光影处理：柔和的光影，避免强烈对比，确保视觉舒适
纹理：轻微的水彩纹理，保持整体平滑感
```

## 用户回答处理策略

### 关键内容提取
1. 识别回答中的主要角色（如"我"、"波波"、"小兔子"等）
2. 识别主要动作和场景（如"打招呼"、"分享"、"帮助"等）
3. 识别情感状态（如"开心"、"害怕"、"兴奋"等）
4. 提取具体物品或环境元素（如"森林"、"玩具"、"食物"等）

### 提示词增强
1. 补充缺失的场景信息（如用户只提到动作但没有场景，则添加绘本中的相关场景）
2. 增强情感描述（确保插画能表达出回答中的情感）
3. 添加自闭症儿童友好的视觉元素（简单清晰的表情，有序的构图）

## 参考图像策略

### 参考图像选择
1. 为每个交互页面前后的插画作为参考
2. 优先使用与当前情境相似的插画作为参考
3. 确保参考图像包含相同的主要角色

### 参考图像使用方式
1. 在API调用中包含参考图像URL
2. 指定参考图像的影响程度（风格影响较高，内容影响较低）
3. 使用多个参考图像以平衡风格一致性

## 风格一致性验证

### 风格检查点
1. 角色外观一致性（确保波波等角色在所有插画中外观一致）
2. 色彩方案一致性（确保使用相同的色彩范围）
3. 线条风格一致性（确保线条粗细和风格一致）
4. 整体氛围一致性（确保插画传达的情感氛围与绘本整体一致）

### 调整策略
1. 如果生成的插画风格不一致，调整提示词中的风格描述
2. 增加更多具体的风格参数
3. 调整参考图像的权重
4. 在极端情况下，提供重新生成选项
